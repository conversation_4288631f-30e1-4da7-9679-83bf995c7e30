#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新的综合评分器
基于新架构的分组管理系统的综合评分器
"""

import pandas as pd
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from core.groups import GroupManager
from core.data import DataLoader


class CompositeScoreResult:
    """综合评分结果类"""
    
    def __init__(self,
                 stock_code: str,
                 stock_name: str,
                 composite_score: float,
                 confidence: float,
                 grade: str,
                 description: str,
                 group_results: Dict[str, Any],
                 group_weights: Dict[str, float],
                 metadata: Optional[Dict[str, Any]] = None):
        self.stock_code = stock_code
        self.stock_name = stock_name
        self.composite_score = composite_score
        self.confidence = confidence
        self.grade = grade
        self.description = description
        self.group_results = group_results
        self.group_weights = group_weights
        self.metadata = metadata or {}
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        def make_json_safe(obj):
            """递归地将对象转换为JSON安全格式"""
            if isinstance(obj, dict):
                return {k: make_json_safe(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [make_json_safe(item) for item in obj]
            elif isinstance(obj, float):
                if obj == float('inf'):
                    return 999999.0  # 用大数代替正无穷
                elif obj == float('-inf'):
                    return -999999.0  # 用大负数代替负无穷
                elif obj != obj:  # NaN检查
                    return 0.0
                else:
                    return obj
            else:
                return obj

        return {
            'stock_code': self.stock_code,
            'stock_name': self.stock_name,
            'composite_score': self.composite_score,
            'confidence': self.confidence,
            'grade': self.grade,
            'description': self.description,
            'group_results': make_json_safe(self.group_results),
            'group_weights': make_json_safe(self.group_weights),
            'metadata': make_json_safe(self.metadata),
            'timestamp': self.timestamp.isoformat()
        }


class NewCompositeScorer:
    """
    新的综合评分器
    
    基于分组管理系统的综合评分器，支持：
    - 动态分组配置
    - 权重调整
    - 多种评分策略
    - 实时配置更新
    """
    
    def __init__(self, group_config_file: str = 'config/group_config.json'):
        """
        初始化综合评分器
        
        Args:
            group_config_file: 分组配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 初始化分组管理器
        self.group_manager = GroupManager(group_config_file)
        
        # 初始化数据加载器
        self.data_loader = DataLoader()
        
        # 评分历史记录
        self.scoring_history: List[CompositeScoreResult] = []

        # 加载历史记录
        self._load_history()
        
        # 配置参数
        self.config = {
            'min_data_points': 50,  # 最少数据点
            'score_normalization': True,  # 是否标准化分数
            'confidence_threshold': 0.3,  # 最低置信度阈值
            'grade_thresholds': {  # 等级阈值
                'A+': 4.0,
                'A': 3.0,
                'B': 2.0,
                'C': 1.0,
                'D': 0.0,
                'E': -2.0,
                'F': float('-inf')
            }
        }
    
    def score_stock(self, stock_code: str, stock_name: str = '', data_limit: int = 250) -> CompositeScoreResult:
        """
        对单只股票进行综合评分

        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            data_limit: 数据量限制

        Returns:
            CompositeScoreResult: 综合评分结果
        """
        try:
            # 获取股票数据
            stock_data = self.data_loader.get_stock_data(stock_code, limit=data_limit)
            
            if stock_data is None or len(stock_data) < self.config['min_data_points']:
                return CompositeScoreResult(
                    stock_code=stock_code,
                    stock_name=stock_name,
                    composite_score=0.0,
                    confidence=0.0,
                    grade='N/A',
                    description='数据不足',
                    group_results={},
                    group_weights={}
                )
            
            # 使用分组管理器计算综合分数
            composite_result = self.group_manager.calculate_composite_score(stock_data)
            
            # 计算等级
            grade = self._calculate_grade(composite_result['composite_score'])
            
            # 创建结果对象
            result = CompositeScoreResult(
                stock_code=stock_code,
                stock_name=stock_name,
                composite_score=composite_result['composite_score'],
                confidence=composite_result['confidence'],
                grade=grade,
                description=composite_result['description'],
                group_results=composite_result['group_results'],
                group_weights=composite_result['group_weights'],
                metadata={
                    'data_points': len(stock_data),
                    'valid_groups': composite_result.get('valid_groups', 0),
                    'config': self.config
                }
            )
            
            # 添加到历史记录
            self.add_to_history(result)
            
            self.logger.info(f"股票 {stock_code} 评分完成: {result.composite_score:.2f} ({grade})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"股票 {stock_code} 评分失败: {str(e)}")
            return CompositeScoreResult(
                stock_code=stock_code,
                stock_name=stock_name,
                composite_score=0.0,
                confidence=0.0,
                grade='ERROR',
                description=f'评分错误: {str(e)}',
                group_results={},
                group_weights={}
            )
    
    def score_multiple_stocks(self, stock_list: List[Dict[str, str]]) -> List[CompositeScoreResult]:
        """
        对多只股票进行批量评分
        
        Args:
            stock_list: 股票列表，格式为 [{'code': '000001', 'name': '平安银行'}, ...]
            
        Returns:
            List[CompositeScoreResult]: 评分结果列表
        """
        results = []
        
        for stock_info in stock_list:
            stock_code = stock_info.get('code', '')
            stock_name = stock_info.get('name', '')
            
            if stock_code:
                result = self.score_stock(stock_code, stock_name)
                results.append(result)
        
        self.logger.info(f"批量评分完成，共处理 {len(results)} 只股票")
        
        return results
    
    def _calculate_grade(self, score: float) -> str:
        """
        根据分数计算等级
        
        Args:
            score: 综合分数
            
        Returns:
            str: 等级
        """
        thresholds = self.config['grade_thresholds']
        
        for grade, threshold in thresholds.items():
            if score >= threshold:
                return grade
        
        return 'F'
    
    def add_to_history(self, result: CompositeScoreResult) -> None:
        """
        添加评分结果到历史记录
        
        Args:
            result: 评分结果
        """
        self.scoring_history.append(result)
        
        # 保持历史记录在合理范围内
        if len(self.scoring_history) > 1000:
            self.scoring_history = self.scoring_history[-500:]
    
    def get_latest_results(self, limit: int = 10) -> List[CompositeScoreResult]:
        """
        获取最新的评分结果
        
        Args:
            limit: 返回结果数量限制
            
        Returns:
            List[CompositeScoreResult]: 最新的评分结果列表
        """
        return self.scoring_history[-limit:] if self.scoring_history else []
    
    def get_group_manager(self) -> GroupManager:
        """
        获取分组管理器
        
        Returns:
            GroupManager: 分组管理器实例
        """
        return self.group_manager
    
    def update_group_weights(self, weights: Dict[str, float]) -> None:
        """
        更新分组权重
        
        Args:
            weights: 新的权重配置
        """
        self.group_manager.set_group_weights(weights)
        self.logger.info("分组权重已更新")
    
    def get_group_weights(self) -> Dict[str, float]:
        """
        获取当前分组权重
        
        Returns:
            Dict[str, float]: 分组权重字典
        """
        return self.group_manager.get_group_weights()
    
    def list_groups(self) -> List[Dict[str, Any]]:
        """
        列出所有分组信息
        
        Returns:
            List[Dict[str, Any]]: 分组信息列表
        """
        return self.group_manager.list_groups()
    
    def create_custom_group(self, group_id: str, name: str, 
                           description: str = '', weight: float = 1.0,
                           custom_config: Dict[str, Any] = None) -> bool:
        """
        创建自定义分组
        
        Args:
            group_id: 分组ID
            name: 分组名称
            description: 分组描述
            weight: 分组权重
            custom_config: 自定义配置
            
        Returns:
            bool: 是否创建成功
        """
        try:
            self.group_manager.create_custom_group(
                group_id, name, description, weight, custom_config
            )
            return True
        except Exception as e:
            self.logger.error(f"创建自定义分组失败: {str(e)}")
            return False
    
    def remove_group(self, group_id: str) -> bool:
        """
        移除分组
        
        Args:
            group_id: 分组ID
            
        Returns:
            bool: 是否移除成功
        """
        try:
            self.group_manager.remove_group(group_id)
            return True
        except Exception as e:
            self.logger.error(f"移除分组失败: {str(e)}")
            return False
    
    def save_config(self) -> None:
        """保存配置"""
        self.group_manager.save_config()
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config: 新的配置参数
        """
        self.config.update(config)
        self.logger.info("综合评分器配置已更新")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取当前配置
        
        Returns:
            Dict[str, Any]: 当前配置
        """
        return self.config.copy()
    
    def get_available_scoring_units(self) -> List[str]:
        """
        获取可用的计分单元类型
        
        Returns:
            List[str]: 可用的计分单元类型列表
        """
        return self.group_manager.get_available_scoring_units()

    def _load_history(self):
        """从CSV文件加载历史记录"""
        import pandas as pd
        import os
        from datetime import datetime

        csv_file = 'storage/analysis_results.csv'

        if not os.path.exists(csv_file):
            self.logger.info("历史记录文件不存在，从空记录开始")
            return

        try:
            df = pd.read_csv(csv_file)
            self.logger.info(f"成功加载 {len(df)} 条分析结果")

            for _, row in df.iterrows():
                # 转换旧格式到新格式
                result = CompositeScoreResult(
                    stock_code=row.get('symbol', ''),
                    stock_name=row.get('name', ''),
                    composite_score=float(row.get('composite_score', 0)),
                    confidence=0.7,  # 默认置信度
                    grade=row.get('composite_grade', 'N/A'),
                    description=row.get('composite_description', ''),
                    group_results={
                        'trend_group': {
                            'weighted_score': float(row.get('trend_score', 0)),
                            'description': row.get('trend_description', ''),
                            'confidence': 0.7
                        },
                        'oscillation_group': {
                            'weighted_score': float(row.get('rsi_score', 0)) + float(row.get('macd_score', 0)),
                            'description': f"RSI: {row.get('rsi_description', '')}, MACD: {row.get('macd_description', '')}",
                            'confidence': 0.6
                        }
                    },
                    group_weights={'trend_group': 0.7, 'oscillation_group': 0.3}
                )

                self.scoring_history.append(result)

            self.logger.info(f"成功加载 {len(self.scoring_history)} 条已保存的分析结果")

        except Exception as e:
            self.logger.error(f"加载历史记录失败: {str(e)}")
