#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取模块
从market_config读取股票配置，获取数据库中的股票数据
"""

import psycopg2
import pandas as pd
import logging
from typing import Dict, List, Optional
# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}

from .csv_storage import CSVStorage

class DataLoader:
    """数据加载器"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.conn = None
        self.logger = logging.getLogger(__name__)
        self.analysis_results = []  # 存储分析结果
        self.csv_storage = CSVStorage()  # CSV存储管理器

        # 尝试加载已保存的分析结果
        self.load_saved_results()

    def load_saved_results(self):
        """加载已保存的分析结果"""
        try:
            saved_results = self.csv_storage.load_analysis_results()
            if saved_results:
                self.analysis_results = saved_results
                self.logger.info(f"成功加载 {len(saved_results)} 条已保存的分析结果")
            else:
                self.logger.info("没有找到已保存的分析结果")
        except Exception as e:
            self.logger.error(f"加载已保存的分析结果失败: {str(e)}")

    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.logger.info("数据库连接成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def save_analysis_result(self, result):
        """保存分析结果到内存"""
        try:
            # 转换结果格式以适配Web界面
            formatted_result = {
                'symbol': result['symbol'],
                'name': result['name'],
                'category': result['category'],
                'trend_score': result['trend_analysis']['score'],
                'trend_grade': result['trend_analysis']['trend_grade'],
                'trend_description': result['trend_analysis']['trend_description'],
                'data_count': result['data_count'],
                'analysis_time': result['analysis_time']
            }

            # 添加RSI信息（如果存在）
            if 'rsi_analysis' in result and result['rsi_analysis']:
                rsi_data = result['rsi_analysis']
                formatted_result.update({
                    'rsi_score': rsi_data.get('score', 0),
                    'rsi_value': rsi_data.get('rsi'),
                    'rsi_signal': rsi_data.get('signal', 'unknown'),
                    'rsi_description': rsi_data.get('description', '')
                })
            else:
                formatted_result.update({
                    'rsi_score': 0,
                    'rsi_value': None,
                    'rsi_signal': 'unknown',
                    'rsi_description': 'RSI数据不可用'
                })

            # 添加MACD信息（如果存在）
            if 'macd_analysis' in result and result['macd_analysis']:
                macd_data = result['macd_analysis']
                formatted_result.update({
                    'macd_score': macd_data.get('score', 0),
                    'macd_value': macd_data.get('macd'),
                    'macd_signal': macd_data.get('signal'),
                    'macd_histogram': macd_data.get('histogram'),
                    'macd_signal_type': macd_data.get('signal_type', 'unknown'),
                    'macd_description': macd_data.get('description', '')
                })
            else:
                formatted_result.update({
                    'macd_score': 0,
                    'macd_value': None,
                    'macd_signal': None,
                    'macd_histogram': None,
                    'macd_signal_type': 'unknown',
                    'macd_description': 'MACD数据不可用'
                })

            # 添加综合评分信息（如果存在）
            if 'composite_analysis' in result and result['composite_analysis']:
                composite_data = result['composite_analysis']
                formatted_result.update({
                    'composite_score': composite_data.get('composite_score', result['trend_analysis']['score']),
                    'composite_grade': composite_data.get('composite_grade', result['trend_analysis']['trend_grade']),
                    'composite_description': composite_data.get('description', result['trend_analysis']['trend_description'])
                })
            else:
                # 如果没有综合评分，使用趋势评分
                formatted_result.update({
                    'composite_score': result['trend_analysis']['score'],
                    'composite_grade': result['trend_analysis']['trend_grade'],
                    'composite_description': result['trend_analysis']['trend_description']
                })

            # 检查是否已存在，如果存在则更新
            existing_index = None
            for i, existing in enumerate(self.analysis_results):
                if existing['symbol'] == result['symbol']:
                    existing_index = i
                    break

            if existing_index is not None:
                self.analysis_results[existing_index] = formatted_result
            else:
                self.analysis_results.append(formatted_result)

            self.logger.debug(f"保存分析结果: {result['symbol']} - 评分: {result['trend_analysis']['score']}")
            return True
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")
            return False

    def get_analysis_results(self, **kwargs):
        """获取分析结果"""
        try:
            page = kwargs.get('page', 1)
            size = kwargs.get('size', 20)
            category = kwargs.get('category', '')
            search = kwargs.get('search', '')
            min_score = kwargs.get('min_score')
            max_score = kwargs.get('max_score')
            sort_by = kwargs.get('sort', 'score')
            order = kwargs.get('order', 'desc')

            # 筛选结果
            filtered_results = self.analysis_results.copy()

            # 按分类筛选
            if category:
                filtered_results = [r for r in filtered_results if r['category'] == category]

            # 按搜索关键词筛选
            if search:
                search_lower = search.lower()
                filtered_results = [r for r in filtered_results
                                  if search_lower in r['symbol'].lower() or search_lower in r['name'].lower()]

            # 按评分筛选（使用综合评分）
            if min_score is not None:
                filtered_results = [r for r in filtered_results if r.get('composite_score', r['trend_score']) >= min_score]
            if max_score is not None:
                filtered_results = [r for r in filtered_results if r.get('composite_score', r['trend_score']) <= max_score]

            # 排序
            reverse = (order == 'desc')
            if sort_by == 'score':
                # 默认使用综合评分排序
                filtered_results.sort(key=lambda x: x.get('composite_score', x['trend_score']), reverse=reverse)
            elif sort_by == 'trend_score':
                filtered_results.sort(key=lambda x: x['trend_score'], reverse=reverse)
            elif sort_by == 'rsi_score':
                filtered_results.sort(key=lambda x: x.get('rsi_score', 0), reverse=reverse)
            elif sort_by == 'rsi_value':
                filtered_results.sort(key=lambda x: x.get('rsi_value', 50) or 50, reverse=reverse)
            elif sort_by == 'macd_score':
                filtered_results.sort(key=lambda x: x.get('macd_score', 0), reverse=reverse)
            elif sort_by == 'macd_value':
                filtered_results.sort(key=lambda x: x.get('macd_value', 0) or 0, reverse=reverse)
            elif sort_by == 'symbol':
                filtered_results.sort(key=lambda x: x['symbol'], reverse=reverse)
            elif sort_by == 'name':
                filtered_results.sort(key=lambda x: x['name'], reverse=reverse)

            # 分页
            total = len(filtered_results)
            start_idx = (page - 1) * size
            end_idx = start_idx + size
            items = filtered_results[start_idx:end_idx]

            pages = (total + size - 1) // size
            has_next = page < pages
            has_prev = page > 1

            return {
                'items': items,
                'total': total,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': total,
                    'pages': pages,
                    'has_next': has_next,
                    'has_prev': has_prev
                }
            }
        except Exception as e:
            self.logger.error(f"获取分析结果失败: {str(e)}")
            return {'items': [], 'total': 0}

    def get_categories(self):
        """获取股票分类"""
        try:
            # 从股票列表中提取分类
            stocks = self.get_stock_list()
            categories = list(set([stock.get('category', '未分类') for stock in stocks]))
            return categories
        except Exception as e:
            self.logger.error(f"获取分类失败: {str(e)}")
            return []

    def get_statistics(self):
        """获取统计信息"""
        try:
            stocks = self.get_stock_list()

            # 基于分析结果计算统计
            positive_count = len([r for r in self.analysis_results if r['trend_score'] > 0])
            negative_count = len([r for r in self.analysis_results if r['trend_score'] < 0])
            neutral_count = len([r for r in self.analysis_results if r['trend_score'] == 0])

            return {
                'total_stocks': len(stocks),
                'analyzed_stocks': len(self.analysis_results),
                'positive_count': positive_count,
                'negative_count': negative_count,
                'neutral_count': neutral_count
            }
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return {
                'total_stocks': 0,
                'analyzed_stocks': 0,
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0
            }

    def search_stocks(self, keyword, limit=10):
        """搜索股票"""
        try:
            stocks = self.get_stock_list()
            results = []
            for stock in stocks:
                if keyword.lower() in stock['symbol'].lower() or keyword.lower() in stock['name'].lower():
                    results.append(stock)
                    if len(results) >= limit:
                        break
            return results
        except Exception as e:
            self.logger.error(f"搜索股票失败: {str(e)}")
            return []

    def close_db(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.logger.info("数据库连接已关闭")
    
    def get_all_data_tables(self) -> List[Dict]:
        """直接从数据库查询所有可用的数据表"""
        if not self.conn:
            if not self.connect_db():
                return []

        try:
            cursor = self.conn.cursor()
            # 查询所有包含OHLCV数据的表
            cursor.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_type = 'BASE TABLE'
                AND table_name NOT LIKE '%_backup%'
                AND table_name NOT LIKE '%_temp%'
                ORDER BY table_name;
            """)

            tables = cursor.fetchall()
            cursor.close()

            # 验证表是否包含必要的列
            valid_tables = []
            for (table_name,) in tables:
                if self._validate_table_structure(table_name):
                    # 解析表名获取股票信息
                    stock_info = self._parse_table_name(table_name)
                    if stock_info:
                        valid_tables.append(stock_info)

            self.logger.info(f"从数据库发现 {len(valid_tables)} 个有效的数据表")
            return valid_tables

        except Exception as e:
            self.logger.error(f"查询数据库表失败: {str(e)}")
            return []
    
    def get_stock_list(self) -> List[Dict]:
        """获取所有可用的股票列表（直接从数据库查询）"""
        return self.get_all_data_tables()

    def _validate_table_structure(self, table_name: str) -> bool:
        """验证表是否包含必要的OHLCV列"""
        if not self.conn:
            return False

        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = %s
                AND table_schema = 'public'
            """, (table_name,))

            columns = [row[0] for row in cursor.fetchall()]
            cursor.close()

            # 检查必要的列是否存在
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            return all(col in columns for col in required_columns)

        except Exception as e:
            self.logger.debug(f"验证表 {table_name} 结构失败: {str(e)}")
            return False

    def _parse_table_name(self, table_name: str) -> Optional[Dict]:
        """从表名解析股票信息"""
        try:
            # 表名格式通常为: prefix_symbol_name
            parts = table_name.split('_', 2)
            if len(parts) < 3:
                return None

            prefix = parts[0]
            symbol = parts[1]
            name = parts[2]

            # 根据前缀和具体内容推断更准确的分类
            category = self._determine_category(prefix, symbol, name, table_name)

            # 清理symbol和name
            clean_symbol = symbol.upper()
            clean_name = name.replace('_', ' ').strip()

            return {
                'symbol': clean_symbol,
                'name': clean_name,
                'category': category,
                'table_name': table_name,
                'source': 'database',
                'enabled': True
            }

        except Exception as e:
            self.logger.debug(f"解析表名 {table_name} 失败: {str(e)}")
            return None

    def _determine_category(self, prefix: str, symbol: str, name: str, table_name: str) -> str:
        """根据表名信息确定9个准确分类"""

        # 1. A股
        if prefix == 'ashares':
            return 'A股'

        # 2. 港股
        elif prefix == 'hkstocks':
            return '港股'

        # 3. 美股
        elif prefix == 'usstocks':
            return '美股'

        # 4. 中国指数
        elif prefix == 'cnindex':
            return '中国指数'

        # 5. 申万行业指数 + 美国指数
        elif prefix == 'other':
            if 'si' in symbol.lower() or '申万' in name:
                return '申万行业指数'
            elif 'spx' in symbol.lower() or 's&p' in name.lower() or 'sp500' in name.lower() or '500指数' in name:
                return '美国指数'
            else:
                return '其他指数'

        # 6. ETF基金
        elif prefix == 'etf':
            return 'ETF基金'

        # 7. 加密货币
        elif prefix == 'crypto':
            return '加密货币'

        # 8. 商品
        elif prefix == 'commodity':
            return '商品'

        # 9. 其他
        else:
            return '其他'
    

    
    def get_stock_data(self, table_name: str, limit: int = 250) -> Optional[pd.DataFrame]:
        """获取股票的历史数据（直接使用表名）"""
        if not self.conn:
            if not self.connect_db():
                return None

        try:
            # 查询最近的数据，按时间倒序
            # 使用引号包围表名以处理数字开头的表名
            query = f"""
                SELECT timestamp, open, high, low, close, volume
                FROM "{table_name}"
                ORDER BY timestamp DESC
                LIMIT %s
            """

            df = pd.read_sql_query(query, self.conn, params=[limit])

            if df.empty:
                self.logger.warning(f"表 {table_name} 没有数据")
                return None

            # 按时间正序排列（计算移动平均线需要）
            df = df.sort_values('timestamp').reset_index(drop=True)
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            self.logger.debug(f"从表 {table_name} 获取到 {len(df)} 条数据")
            return df

        except Exception as e:
            self.logger.error(f"获取表 {table_name} 数据失败: {str(e)}")
            return None
    
    def check_table_exists(self, table_name: str) -> bool:
        """检查数据表是否存在"""
        if not self.conn:
            if not self.connect_db():
                return False

        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = %s
                );
            """, (table_name,))

            exists = cursor.fetchone()[0]
            cursor.close()
            return exists

        except Exception as e:
            self.logger.error(f"检查表 {table_name} 是否存在失败: {str(e)}")
            return False

    def get_available_tables(self) -> List[str]:
        """获取所有可用的数据表"""
        if not self.conn:
            if not self.connect_db():
                return []

        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_type = 'BASE TABLE'
                AND table_name NOT LIKE 'pg_%'
                AND table_name NOT LIKE 'sql_%'
                ORDER BY table_name;
            """)

            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()

            self.logger.info(f"找到 {len(tables)} 个数据表")
            return tables

        except Exception as e:
            self.logger.error(f"获取数据表列表失败: {str(e)}")
            return []

    def close_connection(self):
        """关闭数据库连接"""
        if self.conn:
            try:
                self.conn.close()
                self.conn = None
                self.logger.info("数据库连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {str(e)}")
