#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评分API接口 - 提供评分配置管理、增量分析和结果查询的RESTful API
"""

from flask import Blueprint, request, jsonify
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import traceback

from scoring import ScorerManager, create_scorer_manager

# 创建蓝图
scoring_api = Blueprint('scoring_api', __name__, url_prefix='/api/scoring')

# 全局评分器管理器
scorer_manager: Optional[ScorerManager] = None

def init_scoring_api(config_dir: str = "config", storage_dir: str = "scores"):
    """
    初始化评分API
    
    Args:
        config_dir: 配置目录
        storage_dir: 存储目录
    """
    global scorer_manager
    try:
        scorer_manager = create_scorer_manager(config_dir, storage_dir)
        logging.info("评分API初始化完成")
    except Exception as e:
        logging.error(f"评分API初始化失败: {str(e)}")


@scoring_api.route('/scorers', methods=['GET'])
def get_scorers():
    """获取所有可用的评分器信息"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        scorers = scorer_manager.list_available_scorers()
        
        return jsonify({
            'success': True,
            'data': {
                'scorers': scorers,
                'total_count': len(scorers)
            }
        })
        
    except Exception as e:
        logging.error(f"获取评分器列表失败: {str(e)}")
        return jsonify({'error': f'获取评分器列表失败: {str(e)}'}), 500


@scoring_api.route('/scorers/<scorer_name>', methods=['GET'])
def get_scorer_info(scorer_name: str):
    """获取特定评分器的详细信息"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        scorer_info = scorer_manager.get_scorer_info(scorer_name)
        
        if not scorer_info:
            return jsonify({'error': f'评分器 {scorer_name} 不存在'}), 404
        
        return jsonify({
            'success': True,
            'data': scorer_info
        })
        
    except Exception as e:
        logging.error(f"获取评分器信息失败: {scorer_name}, 错误: {str(e)}")
        return jsonify({'error': f'获取评分器信息失败: {str(e)}'}), 500


@scoring_api.route('/scorers/<scorer_name>/config', methods=['GET', 'PUT'])
def scorer_config(scorer_name: str):
    """获取或更新评分器配置"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        if request.method == 'GET':
            # 获取配置
            scorer_info = scorer_manager.get_scorer_info(scorer_name)
            if not scorer_info:
                return jsonify({'error': f'评分器 {scorer_name} 不存在'}), 404
            
            return jsonify({
                'success': True,
                'data': {
                    'config': scorer_info.get('config', {}),
                    'name': scorer_name,
                    'version': scorer_info.get('version', ''),
                    'score_range': scorer_info.get('score_range', [])
                }
            })
        
        elif request.method == 'PUT':
            # 更新配置
            config_data = request.get_json()
            if not config_data:
                return jsonify({'error': '缺少配置数据'}), 400
            
            success = scorer_manager.update_scorer_config(scorer_name, config_data)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': f'评分器 {scorer_name} 配置已更新'
                })
            else:
                return jsonify({'error': '配置更新失败'}), 500
        
    except Exception as e:
        logging.error(f"评分器配置操作失败: {scorer_name}, 错误: {str(e)}")
        return jsonify({'error': f'配置操作失败: {str(e)}'}), 500


@scoring_api.route('/weights', methods=['GET', 'PUT'])
def weights_config():
    """获取或更新权重配置"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        if request.method == 'GET':
            # 获取权重配置
            weights = scorer_manager.config_manager.get_weights()
            
            return jsonify({
                'success': True,
                'data': {
                    'weights': weights,
                    'total_weight': sum(weights.values())
                }
            })
        
        elif request.method == 'PUT':
            # 更新权重配置
            weights_data = request.get_json()
            if not weights_data or 'weights' not in weights_data:
                return jsonify({'error': '缺少权重数据'}), 400
            
            weights = weights_data['weights']
            
            # 验证权重数据
            for scorer_name, weight in weights.items():
                try:
                    float(weight)
                except (ValueError, TypeError):
                    return jsonify({'error': f'权重值无效: {scorer_name}={weight}'}), 400
            
            # 更新权重
            success_count = 0
            for scorer_name, weight in weights.items():
                if scorer_manager.config_manager.set_weight(scorer_name, float(weight)):
                    success_count += 1
            
            return jsonify({
                'success': True,
                'message': f'已更新 {success_count}/{len(weights)} 个权重配置',
                'updated_weights': weights
            })
        
    except Exception as e:
        logging.error(f"权重配置操作失败: {str(e)}")
        return jsonify({'error': f'权重配置操作失败: {str(e)}'}), 500


@scoring_api.route('/calculate', methods=['POST'])
def calculate_scores():
    """计算股票评分"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        request_data = request.get_json()
        if not request_data:
            return jsonify({'error': '缺少请求数据'}), 400
        
        symbol = request_data.get('symbol')
        if not symbol:
            return jsonify({'error': '缺少股票代码'}), 400
        
        stock_data = request_data.get('data', {})
        force_refresh = request_data.get('force_refresh', False)
        
        # 计算评分
        scores = scorer_manager.calculate_stock_scores(symbol, stock_data, force_refresh)
        
        if not scores:
            return jsonify({'error': '评分计算失败'}), 500
        
        return jsonify({
            'success': True,
            'data': {
                'symbol': symbol,
                'scores': scores,
                'calculated_at': datetime.now().isoformat(),
                'total_scorers': len(scores)
            }
        })
        
    except Exception as e:
        logging.error(f"计算评分失败: {str(e)}")
        return jsonify({'error': f'计算评分失败: {str(e)}'}), 500


@scoring_api.route('/stocks/<symbol>/scores', methods=['GET'])
def get_stock_scores(symbol: str):
    """获取股票的所有评分"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        scores = scorer_manager.get_stock_scores(symbol)
        
        return jsonify({
            'success': True,
            'data': {
                'symbol': symbol,
                'scores': scores,
                'total_scorers': len(scores)
            }
        })
        
    except Exception as e:
        logging.error(f"获取股票评分失败: {symbol}, 错误: {str(e)}")
        return jsonify({'error': f'获取股票评分失败: {str(e)}'}), 500


@scoring_api.route('/stocks/<symbol>/composite', methods=['GET'])
def get_composite_score(symbol: str):
    """获取股票的综合评分"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        composite_type = request.args.get('type', 'comprehensive')
        
        composite_score = scorer_manager.get_composite_score(symbol, composite_type)
        
        if not composite_score:
            return jsonify({'error': '综合评分计算失败或无数据'}), 404
        
        return jsonify({
            'success': True,
            'data': {
                'symbol': symbol,
                'composite_type': composite_type,
                'composite_score': composite_score
            }
        })
        
    except Exception as e:
        logging.error(f"获取综合评分失败: {symbol}, 错误: {str(e)}")
        return jsonify({'error': f'获取综合评分失败: {str(e)}'}), 500


@scoring_api.route('/composite/types', methods=['GET'])
def get_composite_types():
    """获取可用的综合评分类型"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        composite_scorer = scorer_manager.registry.get_scorer('composite')
        if not composite_scorer:
            return jsonify({'error': '综合评分器未找到'}), 404
        
        available_types = composite_scorer.get_available_composite_types()
        
        types_info = []
        for composite_type in available_types:
            type_info = composite_scorer.get_composite_type_info(composite_type)
            types_info.append({
                'type': composite_type,
                'info': type_info
            })
        
        return jsonify({
            'success': True,
            'data': {
                'composite_types': types_info,
                'total_types': len(types_info)
            }
        })
        
    except Exception as e:
        logging.error(f"获取综合评分类型失败: {str(e)}")
        return jsonify({'error': f'获取综合评分类型失败: {str(e)}'}), 500


@scoring_api.route('/storage/stats', methods=['GET'])
def get_storage_stats():
    """获取存储统计信息"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        stats = scorer_manager.get_storage_stats()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logging.error(f"获取存储统计失败: {str(e)}")
        return jsonify({'error': f'获取存储统计失败: {str(e)}'}), 500


@scoring_api.route('/storage/cleanup', methods=['POST'])
def cleanup_storage():
    """清理过期的评分文件"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        cleaned_count = scorer_manager.cleanup_expired_scores()
        
        return jsonify({
            'success': True,
            'data': {
                'cleaned_files': cleaned_count,
                'message': f'已清理 {cleaned_count} 个过期文件'
            }
        })
        
    except Exception as e:
        logging.error(f"清理存储失败: {str(e)}")
        return jsonify({'error': f'清理存储失败: {str(e)}'}), 500


@scoring_api.route('/config/export', methods=['GET'])
def export_config():
    """导出配置"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        config = scorer_manager.export_config()
        
        return jsonify({
            'success': True,
            'data': config
        })
        
    except Exception as e:
        logging.error(f"导出配置失败: {str(e)}")
        return jsonify({'error': f'导出配置失败: {str(e)}'}), 500


@scoring_api.route('/config/import', methods=['POST'])
def import_config():
    """导入配置"""
    try:
        if not scorer_manager:
            return jsonify({'error': '评分器管理器未初始化'}), 500
        
        config_data = request.get_json()
        if not config_data:
            return jsonify({'error': '缺少配置数据'}), 400
        
        success = scorer_manager.import_config(config_data)
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置导入成功'
            })
        else:
            return jsonify({'error': '配置导入失败'}), 500
        
    except Exception as e:
        logging.error(f"导入配置失败: {str(e)}")
        return jsonify({'error': f'导入配置失败: {str(e)}'}), 500


@scoring_api.errorhandler(Exception)
def handle_exception(e):
    """全局异常处理"""
    logging.error(f"API异常: {str(e)}\n{traceback.format_exc()}")
    return jsonify({
        'error': '服务器内部错误',
        'message': str(e)
    }), 500
