#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试API功能（不使用requests）
"""

import urllib.request
import urllib.parse
import json
import time

def test_api():
    """测试API"""
    print("=" * 60)
    print("🌐 测试Web API")
    print("=" * 60)
    
    try:
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(2)
        
        # 请求股票列表（使用POST方法）
        url = "http://127.0.0.1:50506/api/analyze"
        print(f"📡 请求URL: {url}")

        # 构建POST请求
        post_data = json.dumps({'data_limit': 10}).encode('utf-8')
        req = urllib.request.Request(url, data=post_data, headers={'Content-Type': 'application/json'})

        with urllib.request.urlopen(req, timeout=10) as response:
            data = json.loads(response.read().decode())

            print(f"📊 API响应: {data}")

            if 'stock_list' in data:
                stock_list = data['stock_list']
                print(f"✅ 成功获取股票列表，共 {len(stock_list)} 只股票")
                
                # 显示前20只股票
                print(f"\n📋 股票列表预览（前20只）:")
                for i, stock in enumerate(stock_list[:20]):
                    print(f"   {i+1:2d}. {stock['code']:10s} | {stock['name']:25s}")
                
                if len(stock_list) > 20:
                    print(f"   ... 还有 {len(stock_list) - 20} 只股票")
                
                # 分析股票类型
                categories = {}
                for stock in stock_list:
                    code = stock['code']
                    if code.startswith('801'):
                        categories.setdefault('申万行业指数', []).append(stock)
                    elif code.startswith('00') and len(code) == 6:
                        categories.setdefault('A股', []).append(stock)
                    elif code.startswith('0') and len(code) == 5:
                        categories.setdefault('港股', []).append(stock)
                    elif not code.isdigit():
                        categories.setdefault('美股/其他', []).append(stock)
                    else:
                        categories.setdefault('其他', []).append(stock)
                
                print(f"\n📊 股票分类统计:")
                for category, stocks in categories.items():
                    print(f"   {category:15s}: {len(stocks):3d} 只")
                    if category == '申万行业指数' and stocks:
                        print(f"      示例: {stocks[0]['code']} - {stocks[0]['name']}")
                
                # 检查是否包含数据库股票
                db_stocks = len(categories.get('申万行业指数', []))
                if db_stocks > 0:
                    print(f"\n🎉 成功！现在可以看到 {db_stocks} 只申万行业指数（数据库股票）")
                    return True, len(stock_list), db_stocks
                else:
                    print(f"\n❌ 仍然看不到数据库股票")
                    return False, len(stock_list), 0
            else:
                print("❌ 响应中没有stock_list字段")
                return False, 0, 0
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False, 0, 0

def main():
    """主函数"""
    print("🧪 测试Web API股票列表修复效果")
    
    success, total_stocks, db_stocks = test_api()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 修复效果总结")
    print("=" * 60)
    
    if success:
        print("🎉 修复成功！")
        print(f"✅ Web界面现在显示 {total_stocks} 只股票")
        print(f"✅ 其中包含 {db_stocks} 只数据库股票（申万行业指数）")
        print("✅ 用户现在可以在Web界面看到数据库中的股票了")
        print("\n📱 请在浏览器中访问 http://127.0.0.1:50506 查看效果")
    else:
        print("❌ 修复可能不完整")
        print(f"总股票数: {total_stocks}")
        print(f"数据库股票数: {db_stocks}")

if __name__ == "__main__":
    main()
