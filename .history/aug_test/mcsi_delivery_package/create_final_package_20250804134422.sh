#!/bin/bash
# MCSI企业级技术指标最终打包脚本
# 创建安全的交付包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建最终交付包
create_final_package() {
    log_info "创建MCSI企业级技术指标最终交付包..."
    
    # 创建时间戳
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    PACKAGE_NAME="MCSI_Enterprise_Indicators_${TIMESTAMP}"
    
    # 创建交付目录
    mkdir -p "$PACKAGE_NAME"
    
    log_info "📦 准备交付文件..."
    
    # 复制核心文件
    CORE_FILES=(
        "mcsi_macd_secure.pyx"
        "mcsi_rsi_secure.pyx"
        "mcsi_mmt_secure.pyx"
        "mcsi_ttm_secure.pyx"
        "setup.py"
        "build_enterprise.sh"
        "test_enterprise_indicators.py"
        "Dockerfile"
        "README.md"
        "DELIVERY_INSTRUCTIONS.md"
    )
    
    for file in "${CORE_FILES[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$PACKAGE_NAME/"
            log_success "  ✅ 复制: $file"
        else
            log_warning "  ⚠️  文件不存在: $file"
        fi
    done
    
    # 创建安全检查脚本
    cat > "$PACKAGE_NAME/security_check.sh" << 'EOF'
#!/bin/bash
# 安全检查脚本

echo "🔒 MCSI企业级技术指标安全检查"
echo "================================"

# 检查敏感信息
echo "🔍 检查敏感信息..."

SENSITIVE_PATTERNS=(
    "TD9"
    "password"
    "secret"
    "key"
    "token"
    "api_key"
    "private"
    "confidential"
)

FOUND_ISSUES=0

for pattern in "${SENSITIVE_PATTERNS[@]}"; do
    if grep -r -i "$pattern" *.pyx *.py *.md *.sh 2>/dev/null; then
        echo "⚠️  发现敏感信息: $pattern"
        FOUND_ISSUES=$((FOUND_ISSUES + 1))
    fi
done

if [[ $FOUND_ISSUES -eq 0 ]]; then
    echo "✅ 安全检查通过，未发现敏感信息"
else
    echo "❌ 发现 $FOUND_ISSUES 个潜在安全问题"
    exit 1
fi

echo ""
echo "🔒 文件完整性检查..."

REQUIRED_FILES=(
    "mcsi_macd_secure.pyx"
    "mcsi_rsi_secure.pyx"
    "mcsi_mmt_secure.pyx"
    "mcsi_ttm_secure.pyx"
    "setup.py"
    "build_enterprise.sh"
    "README.md"
)

MISSING_FILES=0

for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo "❌ 缺少文件: $file"
        MISSING_FILES=$((MISSING_FILES + 1))
    else
        echo "✅ 文件存在: $file"
    fi
done

if [[ $MISSING_FILES -eq 0 ]]; then
    echo "✅ 文件完整性检查通过"
else
    echo "❌ 缺少 $MISSING_FILES 个必需文件"
    exit 1
fi

echo ""
echo "🎉 所有安全检查通过！"
EOF
    
    chmod +x "$PACKAGE_NAME/security_check.sh"
    
    # 创建版本信息文件
    cat > "$PACKAGE_NAME/VERSION.txt" << EOF
MCSI企业级技术指标模块
版本: 1.0.0
构建时间: $(date)
Python要求: 3.12+
系统要求: Ubuntu 22.04+

包含模块:
- MCSI MACD (移动平均收敛散度)
- MCSI RSI (相对强弱指数)
- MCSI MMT (动量分析)
- MCSI TTM (时序分析)

特性:
- 高性能编译优化
- 源码保护
- 统一接口设计
- 完整测试覆盖
EOF
    
    # 创建快速开始脚本
    cat > "$PACKAGE_NAME/quick_start.sh" << 'EOF'
#!/bin/bash
# MCSI企业级技术指标快速开始脚本

echo "🚀 MCSI企业级技术指标快速开始"
echo "================================"

# 检查系统环境
echo "🔍 检查系统环境..."

if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

PYTHON_VERSION=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
echo "📍 Python版本: $PYTHON_VERSION"

if [[ $(echo "$PYTHON_VERSION < 3.12" | bc -l) -eq 1 ]]; then
    echo "❌ 需要Python 3.12或更高版本"
    exit 1
fi

echo "✅ 系统环境检查通过"

# 运行安全检查
echo ""
echo "🔒 运行安全检查..."
if [[ -f "security_check.sh" ]]; then
    ./security_check.sh
else
    echo "⚠️  安全检查脚本不存在"
fi

# 开始构建
echo ""
echo "🔨 开始构建企业级模块..."
if [[ -f "build_enterprise.sh" ]]; then
    ./build_enterprise.sh
else
    echo "❌ 构建脚本不存在"
    exit 1
fi

echo ""
echo "🎉 快速开始完成！"
echo "📋 下一步:"
echo "   1. 检查生成的.so文件"
echo "   2. 运行测试验证: python3 test_enterprise_indicators.py"
echo "   3. 集成到您的项目中"
EOF
    
    chmod +x "$PACKAGE_NAME/quick_start.sh"
    
    # 运行安全检查
    log_info "🔒 运行安全检查..."
    cd "$PACKAGE_NAME"
    ./security_check.sh
    cd ..
    
    # 创建压缩包
    log_info "📦 创建压缩包..."
    tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_NAME"
    
    # 创建校验和
    sha256sum "${PACKAGE_NAME}.tar.gz" > "${PACKAGE_NAME}.sha256"
    
    # 显示结果
    log_success "🎉 最终交付包创建完成！"
    echo ""
    echo "📦 交付包信息:"
    echo "   文件名: ${PACKAGE_NAME}.tar.gz"
    echo "   大小: $(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)"
    echo "   校验和: ${PACKAGE_NAME}.sha256"
    echo ""
    echo "📋 包含内容:"
    tar -tzf "${PACKAGE_NAME}.tar.gz" | head -20
    echo ""
    echo "🔧 使用方法:"
    echo "   1. 解压: tar -xzf ${PACKAGE_NAME}.tar.gz"
    echo "   2. 进入目录: cd $PACKAGE_NAME"
    echo "   3. 快速开始: ./quick_start.sh"
    echo "   4. 或手动构建: ./build_enterprise.sh"
    
    # 清理临时目录
    if [[ "$1" == "--clean" ]]; then
        log_info "🧹 清理临时目录..."
        rm -rf "$PACKAGE_NAME"
        log_success "清理完成"
    fi
}

# 主函数
main() {
    echo "🚀 MCSI企业级技术指标最终打包"
    echo "================================"
    
    # 检查当前目录
    if [[ ! -f "mcsi_macd_secure.pyx" ]]; then
        log_error "请在包含源文件的目录中运行此脚本"
        exit 1
    fi
    
    # 创建最终包
    create_final_package "$@"
    
    echo ""
    echo "================================"
    log_success "🎉 打包完成！"
    echo ""
    echo "📞 如有问题，请联系技术支持团队"
}

# 执行主函数
main "$@"
