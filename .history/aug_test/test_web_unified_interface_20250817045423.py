#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web系统中的MCSI统一接口
验证新部署的统一接口.so文件是否在Web系统中正常工作
"""

import sys
import json
import logging
from datetime import datetime
import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_unified_interface_import():
    """测试统一接口导入"""
    logger.info("🔧 测试统一接口导入...")
    
    try:
        sys.path.insert(0, '/home/<USER>/Analyze-system')
        
        from core.scoring_units.mcsi_unified_adapter import (
            MCSIUnifiedMACDUnit, MCSIUnifiedRSIUnit, MCSIUnifiedMMTUnit, MCSIUnifiedTTMUnit,
            MCSI_UNIFIED_AVAILABLE
        )
        
        logger.info(f"✅ 统一接口可用性: {MCSI_UNIFIED_AVAILABLE}")
        
        if MCSI_UNIFIED_AVAILABLE:
            # 测试实例化
            macd_unit = MCSIUnifiedMACDUnit()
            rsi_unit = MCSIUnifiedRSIUnit()
            mmt_unit = MCSIUnifiedMMTUnit()
            ttm_unit = MCSIUnifiedTTMUnit()
            
            logger.info(f"✅ MACD单元可用: {macd_unit.is_available()}")
            logger.info(f"✅ RSI单元可用: {rsi_unit.is_available()}")
            logger.info(f"✅ MMT单元可用: {mmt_unit.is_available()}")
            logger.info(f"✅ TTM单元可用: {ttm_unit.is_available()}")
            
            return True
        else:
            logger.warning("⚠️ 统一接口不可用")
            return False
            
    except Exception as e:
        logger.error(f"❌ 统一接口导入失败: {e}")
        return False

def test_web_api_mcsi_data():
    """测试Web API中的MCSI数据（直接调用函数）"""
    logger.info("🌐 测试Web API中的MCSI数据...")

    try:
        # 直接导入Web应用模块
        from web.app import app

        # 创建测试客户端
        with app.test_client() as client:
            # 测试API端点
            response = client.get('/api/stock_chart/000001?limit=50')

            if response.status_code == 200:
                data = response.get_json()

                if data.get('success'):
                    logger.info(f"✅ API调用成功，数据点数: {data.get('data_points', 0)}")

                    # 检查MCSI指标数据
                    mcsi_indicators = data.get('mcsi_indicators', {})

                    for indicator_name in ['mcsi_macd', 'mcsi_rsi', 'mcsi_mmt', 'mcsi_ttm']:
                        indicator_data = mcsi_indicators.get(indicator_name, {})
                        available = indicator_data.get('available', False)

                        if available:
                            score_key = f"{indicator_name.split('_')[1]}_score"
                            scores = indicator_data.get(score_key, [])

                            if scores:
                                # 分析分数
                                valid_scores = [s for s in scores if s is not None and not pd.isna(s)]
                                if valid_scores:
                                    unique_scores = len(set(valid_scores))
                                    score_range = [min(valid_scores), max(valid_scores)]

                                    logger.info(f"  ✅ {indicator_name}: {len(valid_scores)}个有效分数, {unique_scores}个唯一值, 范围{score_range}")

                                    # 检查是否为固定值（异常情况）
                                    if unique_scores == 1:
                                        logger.warning(f"  ⚠️ {indicator_name}: 可能存在固定值问题")
                                    else:
                                        logger.info(f"  🎉 {indicator_name}: 分数正常变化")
                                else:
                                    logger.warning(f"  ⚠️ {indicator_name}: 无有效分数")
                            else:
                                logger.warning(f"  ⚠️ {indicator_name}: 无分数数据")
                        else:
                            logger.warning(f"  ❌ {indicator_name}: 不可用")

                    return True

                else:
                    logger.error(f"❌ API返回失败: {data.get('message', '未知错误')}")
            else:
                logger.error(f"❌ HTTP错误: {response.status_code}")

    except Exception as e:
        logger.error(f"❌ Web API测试失败: {e}")

    return False

def test_professional_chart_access():
    """测试专业图表页面访问"""
    logger.info("📈 测试专业图表页面访问...")

    try:
        # 检查图表模板文件
        from pathlib import Path
        chart_template_path = Path('/home/<USER>/Analyze-system/web/templates/professional_chart.html')

        if chart_template_path.exists():
            logger.info("✅ 专业图表模板文件存在")

            # 检查模板内容中是否包含MCSI相关内容
            with open(chart_template_path, 'r', encoding='utf-8') as f:
                content = f.read()

            mcsi_keywords = ['mcsi_macd', 'mcsi_rsi', 'mcsi_mmt', 'mcsi_ttm']

            for keyword in mcsi_keywords:
                if keyword.lower() in content.lower():
                    logger.info(f"  ✅ 模板包含{keyword}支持")
                else:
                    logger.warning(f"  ⚠️ 模板可能不包含{keyword}支持")

            return True
        else:
            logger.error(f"❌ 图表模板文件不存在: {chart_template_path}")
            return False

    except Exception as e:
        logger.error(f"❌ 图表模板测试失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    logger.info("📋 生成测试报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_type': 'web_unified_interface_integration',
        'results': results,
        'summary': {
            'total_tests': len(results),
            'passed_tests': sum(1 for r in results.values() if r),
            'failed_tests': sum(1 for r in results.values() if not r)
        }
    }
    
    report_path = '/home/<USER>/Analyze-system/aug_test/web_unified_interface_test_report.json'
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 打印汇总
    print("\n" + "="*60)
    print("📊 Web系统MCSI统一接口测试报告")
    print("="*60)
    print(f"测试时间: {report['timestamp']}")
    print(f"总测试数: {report['summary']['total_tests']}")
    print(f"通过测试: {report['summary']['passed_tests']}")
    print(f"失败测试: {report['summary']['failed_tests']}")
    
    print("\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    success_rate = report['summary']['passed_tests'] / report['summary']['total_tests'] * 100
    print(f"\n📈 成功率: {success_rate:.1f}%")
    
    print(f"\n📄 详细报告: {report_path}")
    
    if success_rate == 100:
        print("\n🎉 所有测试通过！Web系统MCSI统一接口集成成功！")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始Web系统MCSI统一接口集成测试...")
    
    results = {}
    
    # 1. 测试统一接口导入
    results['unified_interface_import'] = test_unified_interface_import()
    
    # 2. 测试Web API MCSI数据
    results['web_api_mcsi_data'] = test_web_api_mcsi_data()
    
    # 3. 测试专业图表页面访问
    results['professional_chart_access'] = test_professional_chart_access()
    
    # 4. 生成测试报告
    overall_success = generate_test_report(results)
    
    logger.info("✅ Web系统MCSI统一接口集成测试完成")
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
