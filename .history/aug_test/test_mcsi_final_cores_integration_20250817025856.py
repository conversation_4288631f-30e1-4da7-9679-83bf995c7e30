#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MCSI Final Cores集成
验证100%准确的.so文件在Web系统中的工作状态
"""

import sys
import json
import time
import logging
import pandas as pd
import numpy as np
from pathlib import Path

sys.path.insert(0, '/home/<USER>/Analyze-system')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_final_cores_units_loading():
    """测试Final Cores单元加载"""
    logger.info("🧪 测试MCSI Final Cores单元加载...")
    
    try:
        from core.scoring_units.mcsi_final_cores_units import (
            MCSIFinalRSIUnit, MCSIFinalMACDUnit, MCSIFinalMMTUnit, MCSIFinalTTMUnit,
            MCSI_FINAL_CORES_AVAILABLE
        )
        
        logger.info(f"MCSI Final Cores可用性: {MCSI_FINAL_CORES_AVAILABLE}")
        
        if not MCSI_FINAL_CORES_AVAILABLE:
            logger.error("❌ MCSI Final Cores不可用")
            return False
            
        # 测试创建各个单元
        units = {
            'RSI': MCSIFinalRSIUnit(),
            'MACD': MCSIFinalMACDUnit(), 
            'MMT': MCSIFinalMMTUnit(),
            'TTM': MCSIFinalTTMUnit()
        }
        
        for name, unit in units.items():
            logger.info(f"测试{name}单元:")
            logger.info(f"  - 单元ID: {unit.unit_id}")
            logger.info(f"  - 单元名称: {unit.name}")
            logger.info(f"  - 可用性: {unit.is_available()}")
            logger.info(f"  - 必需列: {unit.get_required_columns()}")
            logger.info(f"  - 最小数据点: {unit.get_min_data_points()}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Final Cores单元加载失败: {e}")
        return False

def test_final_cores_calculation():
    """测试Final Cores计算功能"""
    logger.info("🧮 测试MCSI Final Cores计算功能...")
    
    try:
        from core.scoring_units.mcsi_final_cores_units import MCSIFinalRSIUnit
        
        # 创建测试数据
        np.random.seed(42)
        test_data = pd.DataFrame({
            'open': np.random.uniform(95, 105, 100),
            'high': np.random.uniform(100, 110, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(95, 105, 100),
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # 测试RSI单元
        rsi_unit = MCSIFinalRSIUnit()
        if rsi_unit.is_available():
            result = rsi_unit.calculate_score(test_data)
            logger.info(f"RSI计算结果:")
            logger.info(f"  - 分数: {result.score}")
            logger.info(f"  - 信号: {result.signal}")
            logger.info(f"  - 置信度: {result.confidence}")
            logger.info(f"  - 描述: {result.description}")
            logger.info(f"  - 元数据: {result.metadata}")
            return True
        else:
            logger.error("❌ RSI单元不可用")
            return False
            
    except Exception as e:
        logger.error(f"❌ Final Cores计算测试失败: {e}")
        return False

def test_web_service_integration():
    """测试Web服务集成"""
    logger.info("🌐 测试Web服务集成...")

    try:
        # 测试Web应用导入
        sys.path.insert(0, '/home/<USER>/Analyze-system')
        from web.app import mcsi_rsi_unit, mcsi_macd_unit, mcsi_mmt_unit, mcsi_ttm_unit

        logger.info("Web应用MCSI单元状态:")
        units = {
            'RSI': mcsi_rsi_unit,
            'MACD': mcsi_macd_unit,
            'MMT': mcsi_mmt_unit,
            'TTM': mcsi_ttm_unit
        }

        for name, unit in units.items():
            if unit is not None:
                logger.info(f"  - {name}: ✅ 已加载 ({unit.__class__.__name__})")
                if hasattr(unit, 'is_available'):
                    logger.info(f"    可用性: {unit.is_available()}")
            else:
                logger.warning(f"  - {name}: ❌ 未加载")

        return True

    except Exception as e:
        logger.error(f"❌ Web服务集成测试失败: {e}")
        return False

def test_professional_chart_data():
    """测试专业图表数据"""
    logger.info("📈 测试专业图表数据...")
    
    base_url = "http://127.0.0.1:50505"
    
    try:
        # 测试图表数据API
        response = requests.get(f"{base_url}/api/stock_chart/300584?limit=50", timeout=15)
        
        if response.status_code != 200:
            logger.error(f"❌ 图表API响应异常，状态码: {response.status_code}")
            return False
            
        data = response.json()
        
        # 检查基本数据结构
        required_fields = ['success', 'dates', 'prices', 'mcsi_indicators']
        for field in required_fields:
            if field not in data:
                logger.error(f"❌ 缺少必需字段: {field}")
                return False
                
        logger.info(f"✅ 基本数据结构完整")
        logger.info(f"  - 数据点数: {len(data.get('dates', []))}")
        logger.info(f"  - 价格数据: {len(data.get('prices', []))}")
        
        # 检查MCSI指标数据
        mcsi_indicators = data.get('mcsi_indicators', {})
        expected_indicators = ['mcsi_macd', 'mcsi_mmt', 'mcsi_rsi', 'mcsi_ttm']
        
        logger.info("📊 MCSI指标检查:")
        for indicator in expected_indicators:
            if indicator in mcsi_indicators:
                indicator_data = mcsi_indicators[indicator]
                available = indicator_data.get('available', False)
                score_data = indicator_data.get(f'{indicator.split("_")[1]}_score', [])
                
                logger.info(f"  - {indicator}:")
                logger.info(f"    * 可用性: {'✅' if available else '❌'}")
                logger.info(f"    * 评分数据长度: {len(score_data) if score_data else 0}")
                
                if available and score_data:
                    # 检查评分数据的有效性
                    valid_scores = [s for s in score_data if s is not None and not np.isnan(s)]
                    if valid_scores:
                        logger.info(f"    * 有效评分: {len(valid_scores)}")
                        logger.info(f"    * 评分范围: {min(valid_scores):.2f} ~ {max(valid_scores):.2f}")
                    else:
                        logger.warning(f"    * ⚠️ 无有效评分数据")
            else:
                logger.error(f"  - ❌ 缺少指标: {indicator}")
                
        return True
        
    except Exception as e:
        logger.error(f"❌ 专业图表数据测试失败: {e}")
        return False

def generate_integration_report():
    """生成集成报告"""
    logger.info("📋 生成集成报告...")
    
    report = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'tests': {}
    }
    
    # 运行所有测试
    tests = [
        ('final_cores_loading', test_final_cores_units_loading),
        ('final_cores_calculation', test_final_cores_calculation),
        ('web_service_integration', test_web_service_integration),
        ('professional_chart_data', test_professional_chart_data)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            report['tests'][test_name] = {
                'status': 'PASS' if result else 'FAIL',
                'success': result
            }
        except Exception as e:
            logger.error(f"测试异常: {e}")
            report['tests'][test_name] = {
                'status': 'ERROR',
                'success': False,
                'error': str(e)
            }
    
    # 保存报告
    report_path = Path('/home/<USER>/Analyze-system/aug_test/mcsi_final_cores_integration_report.json')
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成总结
    total_tests = len(report['tests'])
    passed_tests = sum(1 for test in report['tests'].values() if test['success'])
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 集成测试总结")
    logger.info(f"{'='*60}")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    logger.info(f"成功率: {passed_tests/total_tests*100:.1f}%")
    logger.info(f"报告保存至: {report_path}")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！MCSI Final Cores集成成功！")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始MCSI Final Cores集成测试...")
    
    success = generate_integration_report()
    
    if success:
        logger.info("✅ 集成测试完成，系统就绪！")
    else:
        logger.error("❌ 集成测试发现问题，需要修复")
    
    return success

if __name__ == "__main__":
    main()
