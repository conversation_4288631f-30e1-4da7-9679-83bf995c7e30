#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统最终检查脚本
验证MCSI禁用后系统核心功能是否正常
"""

import json
import os
from pathlib import Path

def check_config_files():
    """检查配置文件状态"""
    print("🔍 检查配置文件...")
    
    # 检查必要的配置文件
    config_files = [
        'config/scoring_unit_config.json',
        'config/group_config.json', 
        'config/weight_config.json',
        'config/composite_config.json'
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file} 存在")
        else:
            print(f"❌ {config_file} 缺失")
            return False
    
    return True

def check_mcsi_disabled():
    """检查MCSI禁用状态"""
    print("\n🔍 检查MCSI禁用状态...")
    
    # 检查计分单元配置
    with open('config/scoring_unit_config.json', 'r') as f:
        config = json.load(f)
    
    mcsi_units = ['mcsi_macd_unit', 'mcsi_mmt_unit', 'mcsi_rsi_unit', 'mcsi_ttm_unit']
    
    all_disabled = True
    for unit in mcsi_units:
        enabled = config['scoring_units'][unit]['enabled']
        weight = config['default_weights'][unit]
        
        if enabled or weight > 0:
            print(f"❌ {unit}: enabled={enabled}, weight={weight}")
            all_disabled = False
        else:
            print(f"✅ {unit}: 已禁用")
    
    return all_disabled

def check_traditional_units():
    """检查传统计分单元状态"""
    print("\n🔍 检查传统计分单元...")
    
    with open('config/scoring_unit_config.json', 'r') as f:
        config = json.load(f)
    
    traditional_units = ['rsi_unit', 'macd_unit', 'trend_unit', 'wave_unit']
    
    all_enabled = True
    for unit in traditional_units:
        enabled = config['scoring_units'][unit]['enabled']
        weight = config['default_weights'][unit]
        
        if enabled and weight > 0:
            print(f"✅ {unit}: enabled={enabled}, weight={weight}")
        else:
            print(f"❌ {unit}: enabled={enabled}, weight={weight}")
            all_enabled = False
    
    return all_enabled

def check_tv_code_protection():
    """检查TV-code保护状态"""
    print("\n🔍 检查TV-code保护...")
    
    tv_files = [
        'TV-code/pine-code/MCSI.pine',
        'TV-code/pine-code/MCSI_Logic_Documentation.md',
        'TV-code/pine-code/README.md'
    ]
    
    all_protected = True
    for file_path in tv_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} 已保护")
        else:
            print(f"⚠️ {file_path} 不存在")
            all_protected = False
    
    return all_protected

def check_web_app_status():
    """检查Web应用状态"""
    print("\n🔍 检查Web应用...")
    
    web_app_file = Path('web/app.py')
    if not web_app_file.exists():
        print("❌ web/app.py 不存在")
        return False
    
    with open(web_app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查MCSI禁用标记
    disabled_count = content.count('MCSI功能已禁用')
    print(f"✅ 找到 {disabled_count} 个MCSI禁用标记")
    
    # 检查关键导入是否被注释
    if '# from core.indicators import MCSIMACDIndicator' in content:
        print("✅ MCSI指标导入已注释")
    else:
        print("❌ MCSI指标导入未注释")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始系统最终检查...")
    print("="*50)
    
    # 执行各项检查
    config_ok = check_config_files()
    mcsi_disabled = check_mcsi_disabled()
    traditional_ok = check_traditional_units()
    tv_protected = check_tv_code_protection()
    web_ok = check_web_app_status()
    
    # 生成最终报告
    print("\n" + "="*50)
    print("📊 系统检查报告")
    print("="*50)
    
    print(f"📋 配置文件: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"🚫 MCSI禁用: {'✅ 已禁用' if mcsi_disabled else '❌ 未禁用'}")
    print(f"⚙️ 传统单元: {'✅ 正常' if traditional_ok else '❌ 异常'}")
    print(f"🛡️ TV-code保护: {'✅ 已保护' if tv_protected else '❌ 未保护'}")
    print(f"🌐 Web应用: {'✅ 正常' if web_ok else '❌ 异常'}")
    
    # 总体结果
    all_ok = config_ok and mcsi_disabled and traditional_ok and tv_protected and web_ok
    
    print("\n" + "="*50)
    if all_ok:
        print("🎉 系统检查通过！")
        print("✅ MCSI功能已成功禁用")
        print("✅ 传统功能保持正常")
        print("✅ TV-code核心代码已保护")
        print("💡 可通过修改配置文件随时恢复MCSI功能")
    else:
        print("❌ 系统检查发现问题，请检查上述报告")
    
    print("="*50)
    return all_ok

if __name__ == '__main__':
    main()
