#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证源代码逻辑并生成基准数据
检查TV-code/py-code/mcsi_*.py的核心逻辑，确认分数多样性，生成基准分数CSV
"""

import sys
import os
import numpy as np
import pandas as pd
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import json

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Analyze-system')
sys.path.insert(0, '/home/<USER>/Analyze-system/TV-code/py-code')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_test_scenarios(length: int = 100) -> Dict[str, pd.DataFrame]:
    """生成标准测试数据场景"""
    np.random.seed(42)  # 确保可重复性
    
    scenarios = {}
    
    # 1. 趋势上升场景
    base_price = 100.0
    trend_up = np.linspace(base_price, base_price * 1.3, length)
    noise = np.random.normal(0, 0.02, length) * trend_up
    close_up = trend_up + noise
    
    scenarios['trending_up'] = pd.DataFrame({
        'open': close_up * (1 + np.random.uniform(-0.01, 0.01, length)),
        'high': close_up * (1 + np.random.uniform(0.005, 0.02, length)),
        'low': close_up * (1 + np.random.uniform(-0.02, -0.005, length)),
        'close': close_up,
        'volume': np.random.randint(1000, 10000, length)
    })
    
    # 2. 趋势下降场景
    trend_down = np.linspace(base_price, base_price * 0.7, length)
    noise = np.random.normal(0, 0.02, length) * trend_down
    close_down = trend_down + noise
    
    scenarios['trending_down'] = pd.DataFrame({
        'open': close_down * (1 + np.random.uniform(-0.01, 0.01, length)),
        'high': close_down * (1 + np.random.uniform(0.005, 0.02, length)),
        'low': close_down * (1 + np.random.uniform(-0.02, -0.005, length)),
        'close': close_down,
        'volume': np.random.randint(1000, 10000, length)
    })
    
    # 3. 横盘震荡场景
    sideways = base_price + np.random.normal(0, 0.05, length) * base_price
    
    scenarios['sideways'] = pd.DataFrame({
        'open': sideways * (1 + np.random.uniform(-0.01, 0.01, length)),
        'high': sideways * (1 + np.random.uniform(0.005, 0.02, length)),
        'low': sideways * (1 + np.random.uniform(-0.02, -0.005, length)),
        'close': sideways,
        'volume': np.random.randint(1000, 10000, length)
    })
    
    # 4. 高波动场景
    volatile_base = np.sin(np.linspace(0, 4*np.pi, length)) * 0.2 + 1
    volatile = base_price * volatile_base
    volatile += np.random.normal(0, 0.1, length) * volatile
    
    scenarios['volatile'] = pd.DataFrame({
        'open': volatile * (1 + np.random.uniform(-0.02, 0.02, length)),
        'high': volatile * (1 + np.random.uniform(0.01, 0.05, length)),
        'low': volatile * (1 + np.random.uniform(-0.05, -0.01, length)),
        'close': volatile,
        'volume': np.random.randint(1000, 10000, length)
    })
    
    return scenarios

def test_source_code_indicators(scenarios: Dict[str, pd.DataFrame]) -> Dict:
    """测试源代码指标实现"""
    results = {}
    
    try:
        # 导入源代码指标
        from mcsi_macd import MCSIMACDIndicator
        from mcsi_rsi import MCSIRSIIndicator  
        from mcsi_mmt import MCSIMMTIndicator
        from mcsi_ttm import MCSITTM9Indicator
        
        logger.info("✅ 成功导入所有源代码指标")
        
        # 测试每个场景
        for scenario_name, data in scenarios.items():
            logger.info(f"测试场景: {scenario_name}")
            scenario_results = {}
            
            # 测试MACD
            try:
                macd_indicator = MCSIMACDIndicator(fast_length=19, slow_length=39, signal_length=9, lookback_period=20)
                macd_result = macd_indicator.calculate(data['close'].values)
                macd_scores = macd_result.get('macd_score', [])
                
                scenario_results['macd'] = {
                    'scores': macd_scores.tolist() if hasattr(macd_scores, 'tolist') else macd_scores,
                    'unique_values': len(set(macd_scores)) if len(macd_scores) > 0 else 0,
                    'score_range': [float(np.min(macd_scores)), float(np.max(macd_scores))] if len(macd_scores) > 0 else [0, 0],
                    'non_zero_count': np.count_nonzero(macd_scores) if len(macd_scores) > 0 else 0,
                    'final_score': float(macd_scores[-1]) if len(macd_scores) > 0 else 0.0
                }
                logger.info(f"  MACD: {scenario_results['macd']['unique_values']} 个唯一值, 范围 {scenario_results['macd']['score_range']}")
                
            except Exception as e:
                logger.error(f"  MACD测试失败: {e}")
                scenario_results['macd'] = {'error': str(e)}
            
            # 测试RSI
            try:
                rsi_indicator = MCSIRSIIndicator()
                rsi_result = rsi_indicator.calculate(data['close'].values)
                rsi_scores = rsi_result.get('rsi_score', [])
                
                scenario_results['rsi'] = {
                    'scores': rsi_scores.tolist() if hasattr(rsi_scores, 'tolist') else rsi_scores,
                    'unique_values': len(set(rsi_scores)) if len(rsi_scores) > 0 else 0,
                    'score_range': [float(np.min(rsi_scores)), float(np.max(rsi_scores))] if len(rsi_scores) > 0 else [0, 0],
                    'non_zero_count': np.count_nonzero(rsi_scores) if len(rsi_scores) > 0 else 0,
                    'final_score': float(rsi_scores[-1]) if len(rsi_scores) > 0 else 0.0
                }
                logger.info(f"  RSI: {scenario_results['rsi']['unique_values']} 个唯一值, 范围 {scenario_results['rsi']['score_range']}")
                
            except Exception as e:
                logger.error(f"  RSI测试失败: {e}")
                scenario_results['rsi'] = {'error': str(e)}
            
            # 测试MMT
            try:
                mmt_indicator = MCSIMMTIndicator()
                mmt_result = mmt_indicator.calculate(data['close'].values, data['high'].values, data['low'].values)
                mmt_scores = mmt_result.get('mmt_score', [])
                
                scenario_results['mmt'] = {
                    'scores': mmt_scores.tolist() if hasattr(mmt_scores, 'tolist') else mmt_scores,
                    'unique_values': len(set(mmt_scores)) if len(mmt_scores) > 0 else 0,
                    'score_range': [float(np.min(mmt_scores)), float(np.max(mmt_scores))] if len(mmt_scores) > 0 else [0, 0],
                    'non_zero_count': np.count_nonzero(mmt_scores) if len(mmt_scores) > 0 else 0,
                    'final_score': float(mmt_scores[-1]) if len(mmt_scores) > 0 else 0.0
                }
                logger.info(f"  MMT: {scenario_results['mmt']['unique_values']} 个唯一值, 范围 {scenario_results['mmt']['score_range']}")
                
            except Exception as e:
                logger.error(f"  MMT测试失败: {e}")
                scenario_results['mmt'] = {'error': str(e)}
            
            # 测试TTM
            try:
                ttm_indicator = MCSITTM9Indicator()
                ttm_result = ttm_indicator.calculate(data['close'].values)
                ttm_scores = ttm_result.get('ttm_score', [])
                
                scenario_results['ttm'] = {
                    'scores': ttm_scores.tolist() if hasattr(ttm_scores, 'tolist') else ttm_scores,
                    'unique_values': len(set(ttm_scores)) if len(ttm_scores) > 0 else 0,
                    'score_range': [float(np.min(ttm_scores)), float(np.max(ttm_scores))] if len(ttm_scores) > 0 else [0, 0],
                    'non_zero_count': np.count_nonzero(ttm_scores) if len(ttm_scores) > 0 else 0,
                    'final_score': float(ttm_scores[-1]) if len(ttm_scores) > 0 else 0.0
                }
                logger.info(f"  TTM: {scenario_results['ttm']['unique_values']} 个唯一值, 范围 {scenario_results['ttm']['score_range']}")
                
            except Exception as e:
                logger.error(f"  TTM测试失败: {e}")
                scenario_results['ttm'] = {'error': str(e)}
            
            results[scenario_name] = scenario_results
            
    except ImportError as e:
        logger.error(f"❌ 无法导入源代码指标: {e}")
        return {'error': f'导入失败: {e}'}
    
    return results

def save_benchmark_data(scenarios: Dict[str, pd.DataFrame], results: Dict) -> None:
    """保存基准数据到CSV文件"""
    output_dir = Path('/home/<USER>/Analyze-system/aug_test/benchmark_data')
    output_dir.mkdir(exist_ok=True)
    
    # 保存测试场景数据
    for scenario_name, data in scenarios.items():
        data.to_csv(output_dir / f'scenario_{scenario_name}.csv', index=False)
        logger.info(f"保存场景数据: scenario_{scenario_name}.csv")
    
    # 保存基准分数结果
    benchmark_scores = {}
    
    for scenario_name, scenario_results in results.items():
        if 'error' in scenario_results:
            continue
            
        scenario_scores = {}
        for indicator, indicator_data in scenario_results.items():
            if 'error' not in indicator_data and 'scores' in indicator_data:
                scenario_scores[f'{indicator}_scores'] = indicator_data['scores']
                scenario_scores[f'{indicator}_final_score'] = indicator_data['final_score']
        
        if scenario_scores:
            benchmark_df = pd.DataFrame(dict([(k, pd.Series(v)) for k, v in scenario_scores.items()]))
            benchmark_df.to_csv(output_dir / f'benchmark_{scenario_name}.csv', index=False)
            logger.info(f"保存基准分数: benchmark_{scenario_name}.csv")
            
            benchmark_scores[scenario_name] = scenario_scores
    
    # 保存汇总结果
    with open(output_dir / 'benchmark_summary.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"基准数据保存完成，位置: {output_dir}")

def analyze_score_diversity(results: Dict) -> Dict:
    """分析分数多样性"""
    analysis = {}
    
    for scenario_name, scenario_results in results.items():
        if 'error' in scenario_results:
            continue
            
        scenario_analysis = {}
        
        for indicator, indicator_data in scenario_results.items():
            if 'error' in indicator_data:
                scenario_analysis[indicator] = {'status': 'error', 'error': indicator_data['error']}
                continue
                
            unique_values = indicator_data.get('unique_values', 0)
            score_range = indicator_data.get('score_range', [0, 0])
            non_zero_count = indicator_data.get('non_zero_count', 0)
            
            # 判断分数多样性
            if unique_values <= 1:
                diversity = 'fixed_value'  # 固定值
            elif unique_values <= 3:
                diversity = 'limited'      # 有限多样性
            elif unique_values <= 10:
                diversity = 'moderate'     # 中等多样性
            else:
                diversity = 'high'         # 高多样性
            
            # 判断分数范围
            range_span = abs(score_range[1] - score_range[0])
            if range_span == 0:
                range_type = 'no_variation'
            elif range_span < 10:
                range_type = 'low_variation'
            elif range_span < 50:
                range_type = 'moderate_variation'
            else:
                range_type = 'high_variation'
            
            scenario_analysis[indicator] = {
                'status': 'success',
                'unique_values': unique_values,
                'diversity': diversity,
                'score_range': score_range,
                'range_span': range_span,
                'range_type': range_type,
                'non_zero_ratio': non_zero_count / len(indicator_data.get('scores', [1])),
                'final_score': indicator_data.get('final_score', 0.0)
            }
        
        analysis[scenario_name] = scenario_analysis
    
    return analysis

def main():
    """主函数"""
    logger.info("🚀 开始验证源代码逻辑...")
    
    # 1. 生成测试场景
    logger.info("📊 生成标准测试场景...")
    scenarios = generate_test_scenarios(100)
    logger.info(f"生成了 {len(scenarios)} 个测试场景")
    
    # 2. 测试源代码指标
    logger.info("🧪 测试源代码指标实现...")
    results = test_source_code_indicators(scenarios)
    
    if 'error' in results:
        logger.error(f"❌ 源代码测试失败: {results['error']}")
        return False
    
    # 3. 分析分数多样性
    logger.info("📈 分析分数多样性...")
    diversity_analysis = analyze_score_diversity(results)
    
    # 4. 保存基准数据
    logger.info("💾 保存基准数据...")
    save_benchmark_data(scenarios, results)
    
    # 5. 生成报告
    logger.info("📋 生成验证报告...")
    
    print("\n" + "="*60)
    print("📊 源代码逻辑验证报告")
    print("="*60)
    
    for scenario_name, scenario_analysis in diversity_analysis.items():
        print(f"\n🎯 场景: {scenario_name}")
        print("-" * 40)
        
        for indicator, analysis in scenario_analysis.items():
            if analysis['status'] == 'error':
                print(f"  ❌ {indicator.upper()}: {analysis['error']}")
            else:
                diversity = analysis['diversity']
                range_type = analysis['range_type']
                unique_values = analysis['unique_values']
                score_range = analysis['score_range']
                final_score = analysis['final_score']
                
                status_icon = "✅" if diversity in ['moderate', 'high'] else "⚠️" if diversity == 'limited' else "❌"
                
                print(f"  {status_icon} {indicator.upper()}: {unique_values}个唯一值 ({diversity})")
                print(f"     范围: [{score_range[0]:.2f}, {score_range[1]:.2f}] ({range_type})")
                print(f"     最终分数: {final_score:.2f}")
    
    # 检查是否存在问题
    has_issues = False
    for scenario_analysis in diversity_analysis.values():
        for analysis in scenario_analysis.values():
            if analysis['status'] == 'error' or analysis.get('diversity') == 'fixed_value':
                has_issues = True
                break
    
    if has_issues:
        print(f"\n⚠️ 发现问题：部分指标存在固定值或错误")
        print("建议检查源代码实现和参数配置")
    else:
        print(f"\n✅ 验证通过：所有指标都显示出合理的分数多样性")
    
    logger.info("✅ 源代码逻辑验证完成")
    return not has_issues

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
