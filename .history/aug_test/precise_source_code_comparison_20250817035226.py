#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确源代码对比测试
对同一天（需要前100天历史数据作为输入）的计算结果进行精确对比
"""

import sys
import os
import numpy as np
import pandas as pd
import logging
from pathlib import Path
import json
from typing import Dict, List, Tuple
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Analyze-system')
sys.path.insert(0, '/home/<USER>/Analyze-system/TV-code/py-code')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_real_stock_data(symbol: str = "000001", days: int = 150) -> pd.DataFrame:
    """加载真实股票数据"""
    
    # 尝试从stock_data文件夹加载
    stock_data_dir = Path('/home/<USER>/Analyze-system/stock_data')
    
    # 查找可能的数据文件
    possible_files = [
        stock_data_dir / f"{symbol}.csv",
        stock_data_dir / f"sh{symbol}.csv",
        stock_data_dir / f"sz{symbol}.csv",
        stock_data_dir / "000001.csv",  # 上证指数
        stock_data_dir / "399300.csv",  # 沪深300
    ]
    
    for file_path in possible_files:
        if file_path.exists():
            logger.info(f"找到数据文件: {file_path}")
            try:
                df = pd.read_csv(file_path)
                
                # 标准化列名
                column_mapping = {
                    'Open': 'open', 'open': 'open',
                    'High': 'high', 'high': 'high', 
                    'Low': 'low', 'low': 'low',
                    'Close': 'close', 'close': 'close',
                    'Volume': 'volume', 'volume': 'volume',
                    'Date': 'date', 'date': 'date',
                    'Adj Close': 'adj_close'
                }
                
                df = df.rename(columns=column_mapping)
                
                # 确保有必需的列
                required_cols = ['open', 'high', 'low', 'close']
                if all(col in df.columns for col in required_cols):
                    # 取最近的数据
                    df = df.tail(days).reset_index(drop=True)
                    logger.info(f"成功加载{len(df)}行数据，股票代码: {symbol}")
                    return df
                else:
                    logger.warning(f"文件{file_path}缺少必需列: {required_cols}")
                    
            except Exception as e:
                logger.warning(f"读取文件{file_path}失败: {e}")
                continue
    
    # 如果没有找到真实数据，生成高质量的模拟数据
    logger.warning("未找到真实股票数据，生成高质量模拟数据")
    return generate_realistic_stock_data(symbol, days)

def generate_realistic_stock_data(symbol: str, days: int) -> pd.DataFrame:
    """生成高质量的模拟股票数据"""
    np.random.seed(hash(symbol) % 2**32)
    
    # 基础参数
    base_price = 100.0
    daily_volatility = 0.02
    trend_strength = 0.0005
    
    # 生成价格序列
    returns = np.random.normal(trend_strength, daily_volatility, days)
    log_prices = np.cumsum(returns)
    close_prices = base_price * np.exp(log_prices)
    
    # 生成OHLC数据
    data = []
    for i in range(days):
        close = close_prices[i]
        
        # 生成日内波动
        intraday_range = close * np.random.uniform(0.01, 0.05)
        
        high = close + np.random.uniform(0, intraday_range)
        low = close - np.random.uniform(0, intraday_range)
        open_price = low + np.random.uniform(0, high - low)
        
        # 确保OHLC逻辑正确
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = int(np.random.lognormal(15, 1))  # 对数正态分布的成交量
        
        data.append({
            'date': (datetime.now() - timedelta(days=days-i-1)).strftime('%Y-%m-%d'),
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)

def test_single_day_calculation(data: pd.DataFrame, test_day_index: int = -1) -> Dict:
    """测试单日计算结果的精确对比"""
    logger.info(f"🎯 测试第{test_day_index}天的计算结果...")
    
    if len(data) < 100:
        raise ValueError(f"数据长度不足，需要至少100天历史数据，当前只有{len(data)}天")
    
    # 确保有足够的历史数据
    if test_day_index < 0:
        test_day_index = len(data) + test_day_index
    
    if test_day_index < 100:
        raise ValueError(f"测试日期索引{test_day_index}太早，需要至少100天历史数据")
    
    # 准备输入数据（从开始到测试日期）
    input_data = data.iloc[:test_day_index + 1].copy()
    test_date = input_data.iloc[-1]['date'] if 'date' in input_data.columns else f"Day_{test_day_index}"
    
    logger.info(f"测试日期: {test_date}")
    logger.info(f"输入数据长度: {len(input_data)}天")
    logger.info(f"测试日收盘价: {input_data.iloc[-1]['close']:.2f}")
    
    results = {}
    
    # 测试MACD
    try:
        from mcsi_macd import MCSIMACDIndicator
        
        # 源代码计算
        macd_indicator = MCSIMACDIndicator(fast_length=19, slow_length=39, signal_length=9, lookback_period=20)
        source_result = macd_indicator.calculate(input_data['close'].values)
        source_macd_score = source_result['macd_score'][-1]  # 最后一天的分数
        
        # 统一接口计算
        from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
        unified_unit = MCSIMACDScoringUnit()
        unified_result = unified_unit.calculate_score(ohlc=input_data)
        unified_macd_score = unified_result.score
        
        # 精确对比
        difference = abs(source_macd_score - unified_macd_score)
        
        results['macd'] = {
            'source_score': float(source_macd_score),
            'unified_score': float(unified_macd_score),
            'difference': float(difference),
            'consistent': difference < 1e-6,  # 更严格的容差
            'test_date': test_date,
            'input_days': len(input_data)
        }
        
        logger.info(f"MACD - 源代码: {source_macd_score:.6f}, 统一接口: {unified_macd_score:.6f}, 差异: {difference:.6f}")
        
    except Exception as e:
        logger.error(f"MACD测试失败: {e}")
        results['macd'] = {'error': str(e)}
    
    # 测试RSI
    try:
        from mcsi_rsi import MCSIRSIIndicator
        
        # 源代码计算
        rsi_indicator = MCSIRSIIndicator(dom_cycle=14, vibration=10, leveling=10.0)
        source_result = rsi_indicator.calculate(input_data['close'].values)
        source_rsi_score = source_result['rsi_score'][-1]
        
        # 这里暂时跳过统一接口测试，因为还没实现
        results['rsi'] = {
            'source_score': float(source_rsi_score),
            'unified_score': None,  # 待实现
            'difference': None,
            'consistent': None,
            'test_date': test_date,
            'input_days': len(input_data),
            'note': '统一接口待实现'
        }
        
        logger.info(f"RSI - 源代码: {source_rsi_score:.6f} (统一接口待实现)")
        
    except Exception as e:
        logger.error(f"RSI测试失败: {e}")
        results['rsi'] = {'error': str(e)}
    
    # 测试MMT
    try:
        from mcsi_mmt import MCSIMMTIndicator
        
        # 源代码计算
        mmt_indicator = MCSIMMTIndicator(cyclic_memory=28, leveling=10.0)
        source_result = mmt_indicator.calculate(
            input_data['close'].values,
            input_data['high'].values,
            input_data['low'].values
        )
        source_mmt_score = source_result['mmt_score'][-1]
        
        results['mmt'] = {
            'source_score': float(source_mmt_score),
            'unified_score': None,  # 待实现
            'difference': None,
            'consistent': None,
            'test_date': test_date,
            'input_days': len(input_data),
            'note': '统一接口待实现'
        }
        
        logger.info(f"MMT - 源代码: {source_mmt_score:.6f} (统一接口待实现)")
        
    except Exception as e:
        logger.error(f"MMT测试失败: {e}")
        results['mmt'] = {'error': str(e)}
    
    # 测试TTM
    try:
        from mcsi_ttm import MCSITTMIndicator
        
        # 源代码计算
        ttm_indicator = MCSITTMIndicator(comparison_period=4)
        source_result = ttm_indicator.calculate(input_data['close'].values)
        source_ttm_score = source_result['ttm_score'][-1]
        
        results['ttm'] = {
            'source_score': float(source_ttm_score),
            'unified_score': None,  # 待实现
            'difference': None,
            'consistent': None,
            'test_date': test_date,
            'input_days': len(input_data),
            'note': '统一接口待实现'
        }
        
        logger.info(f"TTM - 源代码: {source_ttm_score:.6f} (统一接口待实现)")
        
    except Exception as e:
        logger.error(f"TTM测试失败: {e}")
        results['ttm'] = {'error': str(e)}
    
    return results

def test_multiple_days(data: pd.DataFrame, test_days: List[int] = None) -> Dict:
    """测试多个日期的计算结果"""
    if test_days is None:
        # 默认测试最后5天
        test_days = list(range(len(data) - 5, len(data)))
    
    # 确保所有测试日期都有足够的历史数据
    test_days = [day for day in test_days if day >= 100]
    
    if not test_days:
        raise ValueError("没有足够历史数据的测试日期")
    
    logger.info(f"测试{len(test_days)}个日期: {test_days}")
    
    all_results = {}
    
    for day_index in test_days:
        try:
            day_result = test_single_day_calculation(data, day_index)
            all_results[f"day_{day_index}"] = day_result
        except Exception as e:
            logger.error(f"测试第{day_index}天失败: {e}")
            all_results[f"day_{day_index}"] = {'error': str(e)}
    
    return all_results

def generate_precise_comparison_report(results: Dict) -> None:
    """生成精确对比报告"""
    logger.info("📋 生成精确对比报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_type': 'precise_source_code_comparison',
        'methodology': '对同一天相同输入数据的源代码vs统一接口精确对比',
        'tolerance': 1e-6,
        'results': results
    }
    
    # 保存详细报告
    report_path = Path('/home/<USER>/Analyze-system/aug_test/precise_comparison_report.json')
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成汇总统计
    total_tests = 0
    passed_tests = 0
    
    print("\n" + "="*70)
    print("📊 精确源代码对比报告")
    print("="*70)
    print(f"测试方法: 对同一天相同输入数据进行源代码vs统一接口精确对比")
    print(f"容差标准: 1e-6 (0.000001)")
    
    for day_key, day_results in results.items():
        print(f"\n🗓️ {day_key}:")
        
        if 'error' in day_results:
            print(f"  ❌ 测试失败: {day_results['error']}")
            continue
        
        for indicator, result in day_results.items():
            if 'error' in result:
                print(f"  ❌ {indicator.upper()}: {result['error']}")
                continue
            
            total_tests += 1
            
            source_score = result.get('source_score')
            unified_score = result.get('unified_score')
            difference = result.get('difference')
            consistent = result.get('consistent')
            
            if unified_score is None:
                print(f"  ⏳ {indicator.upper()}: 源代码={source_score:.6f} (统一接口待实现)")
            elif consistent:
                passed_tests += 1
                print(f"  ✅ {indicator.upper()}: 源代码={source_score:.6f}, 统一接口={unified_score:.6f}, 差异={difference:.6f}")
            else:
                print(f"  ❌ {indicator.upper()}: 源代码={source_score:.6f}, 统一接口={unified_score:.6f}, 差异={difference:.6f} (超出容差)")
    
    print(f"\n📈 测试统计:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过测试: {passed_tests}")
    print(f"  通过率: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "  通过率: N/A")
    
    print(f"\n📄 详细报告: {report_path}")
    
    if passed_tests == total_tests and total_tests > 0:
        print(f"\n🎉 所有已实现的指标都通过了精确对比测试！")
        return True
    else:
        print(f"\n⚠️ 部分测试失败或待实现")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始精确源代码对比测试...")
    
    # 1. 加载真实股票数据
    logger.info("📊 加载股票数据...")
    stock_data = load_real_stock_data("000001", 150)  # 上证指数，150天数据
    
    if len(stock_data) < 120:
        logger.error(f"数据长度不足: {len(stock_data)}天，需要至少120天")
        return False
    
    logger.info(f"数据范围: {stock_data.iloc[0].get('date', 'Day_0')} 到 {stock_data.iloc[-1].get('date', f'Day_{len(stock_data)-1}')}")
    logger.info(f"价格范围: {stock_data['close'].min():.2f} - {stock_data['close'].max():.2f}")
    
    # 2. 测试多个日期
    test_days = [110, 120, 130, 140, len(stock_data) - 1]  # 测试5个不同日期
    test_days = [day for day in test_days if day < len(stock_data)]
    
    logger.info(f"将测试{len(test_days)}个日期...")
    
    # 3. 执行精确对比测试
    results = test_multiple_days(stock_data, test_days)
    
    # 4. 生成报告
    success = generate_precise_comparison_report(results)
    
    logger.info("✅ 精确源代码对比测试完成")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
