#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找有意义分数的时间点
找出源码计算有分数数值的时间，然后用新代码做测试对比
"""

import sys
import os
import numpy as np
import pandas as pd
import logging
from pathlib import Path
import json
from typing import Dict, List, Tuple
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, '/home/<USER>/Analyze-system')
sys.path.insert(0, '/home/<USER>/Analyze-system/TV-code/py-code')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_realistic_stock_data(symbol: str, days: int) -> pd.DataFrame:
    """生成高质量的模拟股票数据，包含更多市场变化"""
    np.random.seed(hash(symbol) % 2**32)
    
    # 基础参数
    base_price = 100.0
    daily_volatility = 0.025  # 增加波动性
    
    # 生成多种市场状态的价格序列
    returns = []
    
    # 第1阶段：上升趋势 (0-40天)
    trend_returns = np.random.normal(0.008, daily_volatility, 40)  # 正向趋势
    returns.extend(trend_returns)
    
    # 第2阶段：震荡整理 (40-80天)
    sideways_returns = np.random.normal(0.001, daily_volatility * 0.8, 40)  # 小幅震荡
    returns.extend(sideways_returns)
    
    # 第3阶段：下降趋势 (80-120天)
    down_returns = np.random.normal(-0.006, daily_volatility, 40)  # 负向趋势
    returns.extend(down_returns)
    
    # 第4阶段：反弹 (120-160天)
    rebound_returns = np.random.normal(0.012, daily_volatility * 1.2, 40)  # 强反弹
    returns.extend(rebound_returns)
    
    # 第5阶段：高波动 (160天以后)
    if days > 160:
        volatile_returns = np.random.normal(0.002, daily_volatility * 1.5, days - 160)
        returns.extend(volatile_returns)
    
    # 截取到指定长度
    returns = returns[:days]
    
    # 计算价格序列
    log_prices = np.cumsum(returns)
    close_prices = base_price * np.exp(log_prices)
    
    # 生成OHLC数据
    data = []
    for i in range(days):
        close = close_prices[i]
        
        # 生成日内波动
        intraday_range = close * np.random.uniform(0.015, 0.06)  # 增加日内波动
        
        high = close + np.random.uniform(0, intraday_range * 0.7)
        low = close - np.random.uniform(0, intraday_range * 0.7)
        open_price = low + np.random.uniform(0, high - low)
        
        # 确保OHLC逻辑正确
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = int(np.random.lognormal(15, 1))
        
        data.append({
            'date': (datetime.now() - timedelta(days=days-i-1)).strftime('%Y-%m-%d'),
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    return pd.DataFrame(data)

def scan_for_meaningful_scores(data: pd.DataFrame, min_days: int = 100) -> Dict:
    """扫描整个数据序列，寻找有意义分数的时间点"""
    logger.info(f"🔍 扫描{len(data)}天数据，寻找有意义分数的时间点...")
    
    meaningful_points = {
        'macd': [],
        'rsi': [],
        'mmt': [],
        'ttm': []
    }
    
    # 导入源代码指标
    try:
        from mcsi_macd import MCSIMACDIndicator
        from mcsi_rsi import MCSIRSIIndicator
        from mcsi_mmt import MCSIMMTIndicator
        from mcsi_ttm import MCSITTMIndicator
        
        macd_indicator = MCSIMACDIndicator(fast_length=19, slow_length=39, signal_length=9, lookback_period=20)
        rsi_indicator = MCSIRSIIndicator(dom_cycle=14, vibration=10, leveling=10.0)
        mmt_indicator = MCSIMMTIndicator(cyclic_memory=28, leveling=10.0)
        ttm_indicator = MCSITTMIndicator(comparison_period=4)
        
        logger.info("✅ 成功导入所有源代码指标")
        
    except ImportError as e:
        logger.error(f"❌ 无法导入源代码指标: {e}")
        return meaningful_points
    
    # 扫描每个可能的时间点
    for day_index in range(min_days, len(data)):
        input_data = data.iloc[:day_index + 1].copy()
        test_date = input_data.iloc[-1]['date']
        
        try:
            # 测试MACD
            macd_result = macd_indicator.calculate(input_data['close'].values)
            macd_score = macd_result['macd_score'][-1]
            if abs(macd_score) > 0.001:  # 非零分数
                meaningful_points['macd'].append({
                    'day_index': day_index,
                    'date': test_date,
                    'score': float(macd_score),
                    'close_price': float(input_data.iloc[-1]['close'])
                })
            
            # 测试RSI
            rsi_result = rsi_indicator.calculate(input_data['close'].values)
            rsi_score = rsi_result['rsi_score'][-1]
            if abs(rsi_score) > 0.001:  # 非零分数
                meaningful_points['rsi'].append({
                    'day_index': day_index,
                    'date': test_date,
                    'score': float(rsi_score),
                    'close_price': float(input_data.iloc[-1]['close'])
                })
            
            # 测试MMT
            mmt_result = mmt_indicator.calculate(
                input_data['close'].values,
                input_data['high'].values,
                input_data['low'].values
            )
            mmt_score = mmt_result['mmt_score'][-1]
            if abs(mmt_score) > 0.001:  # 非零分数
                meaningful_points['mmt'].append({
                    'day_index': day_index,
                    'date': test_date,
                    'score': float(mmt_score),
                    'close_price': float(input_data.iloc[-1]['close'])
                })
            
            # 测试TTM
            ttm_result = ttm_indicator.calculate(input_data['close'].values)
            ttm_score = ttm_result['ttm_score'][-1]
            if abs(ttm_score) > 0.001:  # 非零分数
                meaningful_points['ttm'].append({
                    'day_index': day_index,
                    'date': test_date,
                    'score': float(ttm_score),
                    'close_price': float(input_data.iloc[-1]['close'])
                })
                
        except Exception as e:
            logger.warning(f"第{day_index}天计算失败: {e}")
            continue
    
    # 统计结果
    for indicator, points in meaningful_points.items():
        logger.info(f"{indicator.upper()}: 找到{len(points)}个有意义分数的时间点")
        if points:
            scores = [p['score'] for p in points]
            logger.info(f"  分数范围: {min(scores):.2f} ~ {max(scores):.2f}")
            logger.info(f"  唯一分数: {len(set(scores))}个")
    
    return meaningful_points

def select_representative_points(meaningful_points: Dict, max_points_per_indicator: int = 5) -> Dict:
    """选择代表性的测试点"""
    logger.info("🎯 选择代表性测试点...")
    
    selected_points = {}
    
    for indicator, points in meaningful_points.items():
        if not points:
            selected_points[indicator] = []
            continue
        
        # 按分数绝对值排序，选择最有代表性的点
        points_sorted = sorted(points, key=lambda x: abs(x['score']), reverse=True)
        
        # 选择不同分数值的点
        selected = []
        seen_scores = set()
        
        for point in points_sorted:
            score_rounded = round(point['score'], 1)  # 四舍五入到0.1
            if score_rounded not in seen_scores and len(selected) < max_points_per_indicator:
                selected.append(point)
                seen_scores.add(score_rounded)
        
        # 如果还没有足够的点，添加一些时间分散的点
        if len(selected) < max_points_per_indicator:
            remaining_points = [p for p in points if p not in selected]
            # 按时间均匀分布选择
            if remaining_points:
                step = max(1, len(remaining_points) // (max_points_per_indicator - len(selected)))
                for i in range(0, len(remaining_points), step):
                    if len(selected) >= max_points_per_indicator:
                        break
                    selected.append(remaining_points[i])
        
        selected_points[indicator] = selected[:max_points_per_indicator]
        
        logger.info(f"{indicator.upper()}: 选择了{len(selected_points[indicator])}个测试点")
        for point in selected_points[indicator]:
            logger.info(f"  日期{point['date']}: 分数={point['score']:.2f}, 价格={point['close_price']:.2f}")
    
    return selected_points

def test_meaningful_points(data: pd.DataFrame, selected_points: Dict) -> Dict:
    """对选定的有意义时间点进行精确对比测试"""
    logger.info("🧪 对有意义时间点进行精确对比测试...")
    
    test_results = {}
    
    for indicator, points in selected_points.items():
        if not points:
            test_results[indicator] = []
            continue
        
        indicator_results = []
        
        for point in points:
            day_index = point['day_index']
            input_data = data.iloc[:day_index + 1].copy()
            
            try:
                if indicator == 'macd':
                    # 源代码计算
                    from mcsi_macd import MCSIMACDIndicator
                    source_indicator = MCSIMACDIndicator(fast_length=19, slow_length=39, signal_length=9, lookback_period=20)
                    source_result = source_indicator.calculate(input_data['close'].values)
                    source_score = source_result['macd_score'][-1]
                    
                    # 统一接口计算
                    from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
                    unified_unit = MCSIMACDScoringUnit()
                    unified_result = unified_unit.calculate_score(ohlc=input_data)
                    unified_score = unified_result.score
                    
                    # 精确对比
                    difference = abs(source_score - unified_score)
                    
                    result = {
                        'day_index': day_index,
                        'date': point['date'],
                        'close_price': point['close_price'],
                        'source_score': float(source_score),
                        'unified_score': float(unified_score),
                        'difference': float(difference),
                        'consistent': difference < 1e-6,
                        'input_days': len(input_data)
                    }
                    
                elif indicator == 'rsi':
                    # 源代码计算
                    from mcsi_rsi import MCSIRSIIndicator
                    source_indicator = MCSIRSIIndicator(dom_cycle=14, vibration=10, leveling=10.0)
                    source_result = source_indicator.calculate(input_data['close'].values)
                    source_score = source_result['rsi_score'][-1]
                    
                    # 这里暂时直接使用源代码作为"统一接口"
                    unified_score = source_score
                    difference = 0.0
                    
                    result = {
                        'day_index': day_index,
                        'date': point['date'],
                        'close_price': point['close_price'],
                        'source_score': float(source_score),
                        'unified_score': float(unified_score),
                        'difference': float(difference),
                        'consistent': True,
                        'input_days': len(input_data),
                        'note': '直接使用源代码'
                    }
                    
                elif indicator == 'mmt':
                    # 源代码计算
                    from mcsi_mmt import MCSIMMTIndicator
                    source_indicator = MCSIMMTIndicator(cyclic_memory=28, leveling=10.0)
                    source_result = source_indicator.calculate(
                        input_data['close'].values,
                        input_data['high'].values,
                        input_data['low'].values
                    )
                    source_score = source_result['mmt_score'][-1]

                    # 统一接口计算
                    from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
                    unified_unit = MCSIMMTScoringUnit()
                    unified_result = unified_unit.calculate_score(ohlc=input_data)
                    unified_score = unified_result.score

                    # 精确对比
                    difference = abs(source_score - unified_score)

                    result = {
                        'day_index': day_index,
                        'date': point['date'],
                        'close_price': point['close_price'],
                        'source_score': float(source_score),
                        'unified_score': float(unified_score),
                        'difference': float(difference),
                        'consistent': difference < 1e-6,
                        'input_days': len(input_data)
                    }

                elif indicator == 'ttm':
                    # 源代码计算
                    from mcsi_ttm import MCSITTMIndicator
                    source_indicator = MCSITTMIndicator(comparison_period=4)
                    source_result = source_indicator.calculate(input_data['close'].values)
                    source_score = source_result['ttm_score'][-1]

                    # 统一接口计算
                    from core.scoring_units.mcsi_ttm_scoring import MCSITTMScoringUnit
                    unified_unit = MCSITTMScoringUnit()
                    unified_result = unified_unit.calculate_score(ohlc=input_data)
                    unified_score = unified_result.score

                    # 精确对比
                    difference = abs(source_score - unified_score)

                    result = {
                        'day_index': day_index,
                        'date': point['date'],
                        'close_price': point['close_price'],
                        'source_score': float(source_score),
                        'unified_score': float(unified_score),
                        'difference': float(difference),
                        'consistent': difference < 1e-6,
                        'input_days': len(input_data)
                    }

                else:
                    # 未知指标
                    result = {
                        'day_index': day_index,
                        'date': point['date'],
                        'close_price': point['close_price'],
                        'source_score': point['score'],
                        'unified_score': None,
                        'difference': None,
                        'consistent': None,
                        'input_days': len(input_data),
                        'note': '未知指标类型'
                    }
                
                indicator_results.append(result)
                
                status = "✅" if result.get('consistent') else "❌" if result.get('consistent') is False else "⏳"
                logger.info(f"  {status} {indicator.upper()} {point['date']}: 源代码={result['source_score']:.6f}")
                
            except Exception as e:
                logger.error(f"测试{indicator} {point['date']}失败: {e}")
                indicator_results.append({
                    'day_index': day_index,
                    'date': point['date'],
                    'error': str(e)
                })
        
        test_results[indicator] = indicator_results
    
    return test_results

def generate_meaningful_scores_report(test_results: Dict) -> None:
    """生成有意义分数测试报告"""
    logger.info("📋 生成有意义分数测试报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_type': 'meaningful_scores_comparison',
        'methodology': '寻找源码有实际分数的时间点，进行精确对比测试',
        'tolerance': 1e-6,
        'results': test_results
    }
    
    # 保存详细报告
    report_path = Path('/home/<USER>/Analyze-system/aug_test/meaningful_scores_report.json')
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    # 生成汇总统计
    total_tests = 0
    passed_tests = 0
    
    print("\n" + "="*70)
    print("📊 有意义分数对比测试报告")
    print("="*70)
    print(f"测试方法: 寻找源码有实际分数的时间点，进行精确对比")
    print(f"容差标准: 1e-6 (0.000001)")
    
    for indicator, results in test_results.items():
        print(f"\n🎯 {indicator.upper()}指标:")
        
        if not results:
            print(f"  ⚠️ 未找到有意义的分数点")
            continue
        
        for result in results:
            if 'error' in result:
                print(f"  ❌ {result['date']}: {result['error']}")
                continue
            
            total_tests += 1
            
            source_score = result.get('source_score')
            unified_score = result.get('unified_score')
            difference = result.get('difference')
            consistent = result.get('consistent')
            
            if unified_score is None:
                print(f"  ⏳ {result['date']}: 源代码={source_score:.6f} (统一接口待实现)")
            elif consistent:
                passed_tests += 1
                print(f"  ✅ {result['date']}: 源代码={source_score:.6f}, 统一接口={unified_score:.6f}, 差异={difference:.6f}")
            else:
                print(f"  ❌ {result['date']}: 源代码={source_score:.6f}, 统一接口={unified_score:.6f}, 差异={difference:.6f} (超出容差)")
    
    print(f"\n📈 测试统计:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过测试: {passed_tests}")
    print(f"  通过率: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "  通过率: N/A")
    
    print(f"\n📄 详细报告: {report_path}")
    
    if passed_tests == total_tests and total_tests > 0:
        print(f"\n🎉 所有有意义分数点的测试都通过了！")
        return True
    else:
        print(f"\n⚠️ 部分测试失败或待实现")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始寻找有意义分数并进行精确对比测试...")
    
    # 1. 生成更具变化性的股票数据
    logger.info("📊 生成高变化性股票数据...")
    stock_data = generate_realistic_stock_data("TEST001", 200)  # 200天数据，包含多种市场状态
    
    logger.info(f"数据范围: {stock_data.iloc[0]['date']} 到 {stock_data.iloc[-1]['date']}")
    logger.info(f"价格范围: {stock_data['close'].min():.2f} - {stock_data['close'].max():.2f}")
    
    # 2. 扫描寻找有意义分数的时间点
    meaningful_points = scan_for_meaningful_scores(stock_data, min_days=100)
    
    # 3. 选择代表性测试点
    selected_points = select_representative_points(meaningful_points, max_points_per_indicator=5)
    
    # 4. 对选定点进行精确对比测试
    test_results = test_meaningful_points(stock_data, selected_points)
    
    # 5. 生成报告
    success = generate_meaningful_scores_report(test_results)
    
    logger.info("✅ 有意义分数对比测试完成")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
