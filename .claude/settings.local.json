{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(pip --version)", "<PERSON><PERSON>(python3 -m pip:*)", "Bash(apt list:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(ls:*)", "Ba<PERSON>(pip3:*)", "<PERSON><PERSON>(python3.12:*)", "Bash(sudo add-apt-repository:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(python:*)", "Bash(./start_service.sh:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:medium.com)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:github.com)", "WebFetch(domain:www.claudemcp.com)", "WebFetch(domain:scottspence.com)", "Bash(claude mcp add:*)", "Bash(/home/<USER>/.nvm/versions/node/v22.18.0/bin/claude mcp add fintech-postgres npx -- -y @modelcontextprotocol/server-postgres \"************************************************/fintech_db\")", "Bash(/home/<USER>/.nvm/versions/node/v22.18.0/bin/claude mcp list)", "Bash(grep:*)", "Bash(git reset:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(kill:*)", "Bash(git add:*)", "Bash(git config:*)", "mcp__fintech-postgres__query", "Bash(ss:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(pgrep:*)", "<PERSON><PERSON>(sed:*)", "Bash(timeout 60 python3 claude_test/test_exception_handling.py)", "mcp__ide__executeCode", "Bash(pip install:*)", "Bash(CFLAGS=\"-O0\" python3 setup.py build_ext --inplace)", "<PERSON><PERSON>(pip show:*)", "Bash(timeout 90 python3 claude_test/performance_monitor.py)", "<PERSON><PERSON>(true)", "Bash(export:*)", "Bash(PYTHONPATH=/home/<USER>/Analyze-system python3 web/app.py)", "Bash(CFLAGS=\"-O0 -g\" python3 setup_fixed.py build_ext --inplace)", "Bash(CFLAGS=\"-O0 -g\" python3 setup_exact.py build_ext --inplace)", "Bash(CFLAGS=\"-O0 -g\" python3 claude_test/setup_exact.py build_ext --inplace)", "Bash(CFLAGS=\"-O0 -g\" python3 setup_all_core.py build_ext --inplace)", "Bash(rm:*)", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_macd_core'',\n    [''mcsi_macd_exact.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "<PERSON><PERSON>(echo:*)", "Bash(PYTHONPATH=/home/<USER>/Analyze-system python3 ../main.py)", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core_fixed'',\n    [''claude_test/mcsi_mmt_exact.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core_complete'',\n    [''claude_test/mcsi_mmt_exact.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core_final'',\n    [''claude_test/mcsi_mmt_exact.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core'',\n    [''claude_test/mcsi_mmt_exact.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core'',\n    [''test_simple_mmt.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core'',\n    [''claude_test/test_simple_mmt.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core'',\n    [''claude_test/test_simple_mmt.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core'',\n    [''claude_test/mcsi_mmt_working.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")", "Bash(CFLAGS=\"-O0 -g\" python3 -c \"\nfrom setuptools import setup, Extension\nfrom Cython.Build import cythonize\nimport numpy as np\n\next = Extension(\n    ''mcsi_mmt_core'',\n    [''claude_test/mcsi_mmt_working.pyx''],\n    include_dirs=[np.get_include()],\n    define_macros=[(''NPY_NO_DEPRECATED_API'', ''NPY_1_7_API_VERSION'')],\n    extra_compile_args=[''-std=c99'', ''-O0'', ''-g''],\n)\n\nsetup(\n    ext_modules=cythonize([ext], \n                         compiler_directives={\n                             ''language_level'': ''3str'',\n                             ''boundscheck'': False,\n                             ''wraparound'': False,\n                         }),\n    zip_safe=False,\n    script_name=''setup.py'',\n    script_args=[''build_ext'', ''--inplace'']\n)\n\")"], "deny": []}}